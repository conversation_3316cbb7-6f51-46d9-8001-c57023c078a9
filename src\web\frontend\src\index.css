@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  --color-primary: 59 130 246;
  --color-primary-foreground: 255 255 255;
  --color-secondary: 156 163 175;
  --color-secondary-foreground: 17 24 39;
  --color-muted: 243 244 246;
  --color-muted-foreground: 107 114 128;
  --color-accent: 249 250 251;
  --color-accent-foreground: 17 24 39;
  --color-destructive: 239 68 68;
  --color-destructive-foreground: 255 255 255;
  --color-border: 229 231 235;
  --color-input: 255 255 255;
  --color-ring: 59 130 246;
  --radius: 0.5rem;
}

.dark {
  --color-primary: 59 130 246;
  --color-primary-foreground: 255 255 255;
  --color-secondary: 55 65 81;
  --color-secondary-foreground: 243 244 246;
  --color-muted: 31 41 55;
  --color-muted-foreground: 156 163 175;
  --color-accent: 31 41 55;
  --color-accent-foreground: 243 244 246;
  --color-destructive: 239 68 68;
  --color-destructive-foreground: 255 255 255;
  --color-border: 55 65 81;
  --color-input: 31 41 55;
  --color-ring: 59 130 246;
}

/* Base styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Scrollbar styles */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
  
  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
  
  .dark ::selection {
    @apply bg-primary-900 text-primary-100;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-ring disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700 dark:active:bg-gray-600;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 dark:active:bg-gray-600;
  }
  
  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 active:bg-gray-200 dark:text-gray-300 dark:hover:bg-gray-800 dark:active:bg-gray-700;
  }
  
  .btn-destructive {
    @apply bg-red-600 text-white hover:bg-red-700 active:bg-red-800;
  }
  
  /* Button sizes */
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-10 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-12 px-6 text-base;
  }
  
  /* Input styles */
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-ring disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-400;
  }
  
  .textarea {
    @apply flex min-h-[80px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-ring disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-400;
  }
  
  /* Card styles */
  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100;
  }
  
  .card-description {
    @apply text-sm text-gray-600 dark:text-gray-400;
  }
  
  .card-content {
    @apply p-6 pt-0;
  }
  
  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .badge-default {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200;
  }
  
  .badge-primary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
  }
  
  .badge-error {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Skeleton loading */
  .skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700;
  }
  
  /* Code block styles */
  .code-block {
    @apply rounded-md bg-gray-100 p-4 font-mono text-sm dark:bg-gray-800;
  }
  
  .code-inline {
    @apply rounded bg-gray-100 px-1.5 py-0.5 font-mono text-sm dark:bg-gray-800;
  }
  
  /* Gradient backgrounds */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }
  
  .gradient-secondary {
    @apply bg-gradient-to-r from-gray-500 to-gray-600;
  }
  
  .gradient-success {
    @apply bg-gradient-to-r from-green-500 to-green-600;
  }
  
  .gradient-warning {
    @apply bg-gradient-to-r from-yellow-500 to-yellow-600;
  }
  
  .gradient-error {
    @apply bg-gradient-to-r from-red-500 to-red-600;
  }
}

/* Utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
  
  /* Custom shadows */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
  }
  
  /* Glass morphism */
  .glass {
    @apply backdrop-blur-sm bg-white/80 dark:bg-gray-900/80;
  }
  
  .glass-strong {
    @apply backdrop-blur-md bg-white/90 dark:bg-gray-900/90;
  }
}

/* Monaco Editor theme adjustments */
.monaco-editor {
  @apply rounded-md;
}

.monaco-editor .margin {
  @apply bg-gray-50 dark:bg-gray-800;
}

/* Mermaid diagram styles */
.mermaid {
  @apply flex justify-center;
}

.mermaid svg {
  @apply max-w-full h-auto;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  .print-break-inside-avoid {
    page-break-inside: avoid;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn {
    @apply border-2;
  }
  
  .card {
    @apply border-2;
  }
  
  .input {
    @apply border-2;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
