#!/bin/bash

# Architek-AI Build Script
# Builds the entire project including CLI, web API, and frontend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 16+ and try again."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        log_error "Node.js version 16+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    log_info "Node.js version: $(node --version)"
}

# Check if npm is installed
check_npm() {
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    log_info "npm version: $(npm --version)"
}

# Clean previous builds
clean_build() {
    log_info "Cleaning previous builds..."
    
    if [ -d "dist" ]; then
        rm -rf dist
        log_info "Removed dist directory"
    fi
    
    if [ -d "src/web/frontend/dist" ]; then
        rm -rf src/web/frontend/dist
        log_info "Removed frontend dist directory"
    fi
    
    if [ -d "coverage" ]; then
        rm -rf coverage
        log_info "Removed coverage directory"
    fi
}

# Install dependencies
install_dependencies() {
    log_info "Installing root dependencies..."
    npm ci
    
    if [ -d "src/web/frontend" ]; then
        log_info "Installing frontend dependencies..."
        cd src/web/frontend
        npm ci
        cd ../../..
    fi
    
    log_success "Dependencies installed"
}

# Run linting
run_lint() {
    log_info "Running ESLint..."
    npm run lint
    log_success "Linting completed"
}

# Run tests
run_tests() {
    log_info "Running tests..."
    npm run test:coverage
    log_success "Tests completed"
}

# Build TypeScript
build_typescript() {
    log_info "Building TypeScript..."
    npm run build:cli
    log_success "TypeScript build completed"
}

# Build frontend
build_frontend() {
    if [ -d "src/web/frontend" ]; then
        log_info "Building frontend..."
        cd src/web/frontend
        npm run build
        cd ../../..
        log_success "Frontend build completed"
    else
        log_warning "Frontend directory not found, skipping frontend build"
    fi
}

# Create package
create_package() {
    log_info "Creating package..."
    npm pack
    log_success "Package created"
}

# Validate build
validate_build() {
    log_info "Validating build..."
    
    # Check if CLI entry point exists
    if [ ! -f "dist/cli/index.js" ]; then
        log_error "CLI entry point not found: dist/cli/index.js"
        exit 1
    fi
    
    # Check if CLI is executable
    if ! node dist/cli/index.js --version &> /dev/null; then
        log_error "CLI is not executable"
        exit 1
    fi
    
    # Check if web API entry point exists
    if [ ! -f "dist/web/api/server.js" ]; then
        log_warning "Web API entry point not found: dist/web/api/server.js"
    fi
    
    log_success "Build validation completed"
}

# Main build process
main() {
    log_info "Starting Architek-AI build process..."
    
    # Parse command line arguments
    SKIP_TESTS=false
    SKIP_LINT=false
    SKIP_FRONTEND=false
    PRODUCTION=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --skip-lint)
                SKIP_LINT=true
                shift
                ;;
            --skip-frontend)
                SKIP_FRONTEND=true
                shift
                ;;
            --production)
                PRODUCTION=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --skip-tests      Skip running tests"
                echo "  --skip-lint       Skip linting"
                echo "  --skip-frontend   Skip frontend build"
                echo "  --production      Production build"
                echo "  -h, --help        Show this help message"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Set environment
    if [ "$PRODUCTION" = true ]; then
        export NODE_ENV=production
        log_info "Building for production"
    else
        export NODE_ENV=development
        log_info "Building for development"
    fi
    
    # Run build steps
    check_node
    check_npm
    clean_build
    install_dependencies
    
    if [ "$SKIP_LINT" = false ]; then
        run_lint
    fi
    
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    fi
    
    build_typescript
    
    if [ "$SKIP_FRONTEND" = false ]; then
        build_frontend
    fi
    
    validate_build
    
    if [ "$PRODUCTION" = true ]; then
        create_package
    fi
    
    log_success "Build completed successfully!"
    
    # Display build summary
    echo ""
    echo "Build Summary:"
    echo "=============="
    echo "Environment: $NODE_ENV"
    echo "CLI: $([ -f "dist/cli/index.js" ] && echo "✓" || echo "✗")"
    echo "Web API: $([ -f "dist/web/api/server.js" ] && echo "✓" || echo "✗")"
    echo "Frontend: $([ -d "src/web/frontend/dist" ] && echo "✓" || echo "✗")"
    echo "Tests: $([ "$SKIP_TESTS" = false ] && echo "✓" || echo "Skipped")"
    echo "Linting: $([ "$SKIP_LINT" = false ] && echo "✓" || echo "Skipped")"
    
    if [ "$PRODUCTION" = true ]; then
        echo "Package: $(ls -1 architek-ai-*.tgz 2>/dev/null | head -1 || echo "Not created")"
    fi
    
    echo ""
    log_info "You can now run: node dist/cli/index.js --help"
}

# Run main function
main "$@"
