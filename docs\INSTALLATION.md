# ArchitekAI Installation Guide

This guide provides step-by-step instructions for installing and setting up ArchitekAI on your system.

## System Requirements

### Minimum Requirements
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **Memory**: 512 MB RAM
- **Storage**: 100 MB free space

### Recommended Requirements
- **Node.js**: >= 18.0.0
- **npm**: >= 9.0.0
- **Memory**: 1 GB RAM
- **Storage**: 500 MB free space

## Installation Methods

### Method 1: Global Installation (Recommended)

This method installs ArchitekAI globally, making the `architek-ai` command available system-wide.

```bash
# 1. Clone the repository
git clone https://github.com/HectorTa1989/ArchitekAI.git
cd ArchitekAI

# 2. Install dependencies
npm install

# 3. Install frontend dependencies
cd src/web/frontend
npm install
cd ../../..

# 4. Build the project
npm run build

# 5. Install globally
npm install -g .

# 6. Verify installation
architek-ai --version
architek-ai --help
```

### Method 2: Local Development Setup

For development or if you prefer not to install globally:

```bash
# 1. Clone and setup (same as above)
git clone https://github.com/HectorTa1989/ArchitekAI.git
cd ArchitekAI
npm install
cd src/web/frontend && npm install && cd ../../..
npm run build

# 2. Use with node directly
node dist/cli/index.js --help

# 3. Create an alias (optional)
echo 'alias architek-ai="node $(pwd)/dist/cli/index.js"' >> ~/.bashrc
source ~/.bashrc
```

### Method 3: Docker Installation (Coming Soon)

```bash
# Pull the Docker image
docker pull architekai/architek-ai:latest

# Run with Docker
docker run --rm -v $(pwd):/workspace architekai/architek-ai generate "microservices architecture"
```

## Post-Installation Setup

### 1. Verify Installation

```bash
# Check version
architek-ai --version

# Test basic functionality
architek-ai generate "simple web app" --no-interactive

# List available templates
architek-ai template list
```

### 2. Configuration

Create a configuration file (optional):

```bash
# Create config directory
mkdir -p ~/.architekai

# Create basic configuration
cat > ~/.architekai/config.yaml << EOF
output:
  defaultFormat: "mermaid"
  directory: "./diagrams"

cli:
  interactive: true
  verbose: false
  colors: true

logging:
  level: "info"

nlp:
  confidence:
    minimum: 0.3
    warning: 0.6

templates:
  directory: "./templates"
  autoLoad: true
  validation: true
EOF
```

### 3. Test Generation

```bash
# Create a test directory
mkdir -p ~/architek-ai-test
cd ~/architek-ai-test

# Generate your first diagram
architek-ai generate "microservices architecture with API gateway, user service, and payment service" --no-interactive

# Check the output
ls -la diagrams/
cat diagrams/*.mmd
```

## Troubleshooting

### Common Issues

#### 1. Command Not Found

**Error**: `architek-ai: command not found`

**Solutions**:
```bash
# Check if globally installed
npm list -g architek-ai

# Reinstall globally
cd /path/to/ArchitekAI
npm install -g .

# Check PATH
echo $PATH
which architek-ai
```

#### 2. Module Resolution Errors

**Error**: `Cannot find module '@/utils/logger'`

**Solutions**:
```bash
# Rebuild the project
npm run clean
npm run build

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
npm run build
```

#### 3. Permission Errors

**Error**: `EACCES: permission denied`

**Solutions**:
```bash
# Use npm prefix for global installs
npm config set prefix ~/.npm-global
export PATH=~/.npm-global/bin:$PATH

# Or use sudo (not recommended)
sudo npm install -g .
```

#### 4. Frontend Build Failures

**Error**: Frontend build fails during installation

**Solutions**:
```bash
# Install frontend dependencies separately
cd src/web/frontend
npm install
npm run build
cd ../../..

# Build only CLI if frontend not needed
npm run build:cli
```

### Getting Help

If you encounter issues:

1. **Check the logs**: Enable verbose logging with `--verbose`
2. **Search issues**: Check [GitHub Issues](https://github.com/HectorTa1989/ArchitekAI/issues)
3. **Create an issue**: Provide system info, error messages, and steps to reproduce

### System Information

To help with troubleshooting, gather system information:

```bash
# Node.js and npm versions
node --version
npm --version

# Operating system
uname -a  # Linux/macOS
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"  # Windows

# ArchitekAI version and config
architek-ai --version
architek-ai config show
```

## Updating

### Update to Latest Version

```bash
# Navigate to ArchitekAI directory
cd /path/to/ArchitekAI

# Pull latest changes
git pull origin main

# Update dependencies
npm install
cd src/web/frontend && npm install && cd ../../..

# Rebuild
npm run build

# Reinstall globally
npm install -g .
```

### Uninstalling

```bash
# Uninstall global package
npm uninstall -g architek-ai

# Remove configuration (optional)
rm -rf ~/.architekai

# Remove cloned repository
rm -rf /path/to/ArchitekAI
```

## Next Steps

After successful installation:

1. **Read the documentation**: Check out [API.md](API.md) for command reference
2. **Try examples**: Generate some example diagrams to familiarize yourself
3. **Explore templates**: Use `architek-ai template list` to see available patterns
4. **Configure settings**: Customize ArchitekAI to your preferences
5. **Integrate with workflow**: Add ArchitekAI to your development process

## Support

- **Documentation**: [docs/](.)
- **GitHub**: [https://github.com/HectorTa1989/ArchitekAI](https://github.com/HectorTa1989/ArchitekAI)
- **Issues**: [GitHub Issues](https://github.com/HectorTa1989/ArchitekAI/issues)
