# ArchitekAI API Reference

This document provides comprehensive API reference for ArchitekAI's CLI commands and configuration options.

## CLI Commands

### `architek-ai generate`

Generate architecture diagrams from natural language descriptions.

#### Syntax
```bash
architek-ai generate [description] [options]
```

#### Parameters
- `description` (optional): Natural language description of the architecture

#### Options
- `-o, --output <path>`: Output file path
- `-f, --format <format>`: Output format (mermaid, plantuml, ascii, drawio, lucidchart)
- `-t, --template <name>`: Use a specific template as base
- `-i, --interactive`: Enable interactive mode for refinement
- `-s, --style <path>`: Path to custom style configuration
- `--validate`: Validate the generated diagram
- `--preview`: Preview the diagram before saving
- `--overwrite`: Overwrite existing files without confirmation
- `--verbose`: Enable verbose output

#### Global Options
- `--config <path>`: Path to configuration file
- `--verbose`: Enable verbose logging
- `--quiet`: Suppress non-error output
- `--no-colors`: Disable colored output
- `--no-interactive`: Disable interactive prompts

#### Examples
```bash
# Basic generation
architek-ai generate "microservices with API gateway"

# With template
architek-ai generate "e-commerce platform" --template microservices

# Non-interactive mode
architek-ai generate "web app" --no-interactive

# Custom output
architek-ai generate "system" --output ./my-diagram.mmd --format mermaid
```

### `architek-ai template`

Manage architecture templates.

#### Subcommands

##### `template list`
List available templates.

```bash
architek-ai template list [options]
```

**Options:**
- `--pattern <pattern>`: Filter by pattern name
- `--complexity <level>`: Filter by complexity (simple, moderate, complex)
- `--format <format>`: Output format (table, json, yaml)
- `--verbose`: Show detailed information

##### `template show`
Show template details.

```bash
architek-ai template show <name> [options]
```

**Options:**
- `--format <format>`: Output format (yaml, json)
- `--verbose`: Show all details

##### `template create`
Create a new template.

```bash
architek-ai template create [options]
```

**Options:**
- `--name <name>`: Template name
- `--pattern <pattern>`: Architecture pattern
- `--description <desc>`: Template description
- `--interactive`: Interactive creation mode

### `architek-ai config`

Manage configuration settings.

#### Subcommands

##### `config show`
Display current configuration.

```bash
architek-ai config show [options]
```

##### `config set`
Set configuration values.

```bash
architek-ai config set [options]
```

**Options:**
- `--output-format <format>`: Default output format
- `--output-directory <path>`: Default output directory
- `--log-level <level>`: Logging level (error, warn, info, debug)
- `--interactive <boolean>`: Enable interactive mode by default
- `--verbose <boolean>`: Enable verbose output by default
- `--colors <boolean>`: Enable colored output

##### `config reset`
Reset configuration to defaults.

```bash
architek-ai config reset [options]
```

### `architek-ai batch`

Process multiple architecture descriptions in batch.

```bash
architek-ai batch [options]
```

**Options:**
- `--input <pattern>`: Input file pattern
- `--output <directory>`: Output directory
- `--format <format>`: Output format for all files
- `--template <name>`: Template to use for all files
- `--parallel <number>`: Number of parallel processes
- `--dry-run`: Show what would be processed without generating
- `--continue-on-error`: Continue processing if one file fails
- `--exclude <pattern>`: Exclude files by name pattern

### `architek-ai export`

Export diagrams to different formats.

```bash
architek-ai export [options]
```

**Options:**
- `--input <path>`: Input diagram file or directory
- `--output <path>`: Output path
- `--format <format>`: Target format
- `--style <path>`: Style configuration file

## Configuration File

ArchitekAI uses YAML configuration files. Default location: `~/.architekai/config.yaml`

### Configuration Schema

```yaml
# Output settings
output:
  defaultFormat: "mermaid"
  directory: "./diagrams"
  
# CLI behavior
cli:
  interactive: true
  verbose: false
  colors: true
  
# Logging
logging:
  level: "info"
  file: "./logs/architek-ai.log"
  
# NLP settings
nlp:
  confidence:
    minimum: 0.3
    warning: 0.6
    
# Template settings
templates:
  directory: "./templates"
  autoLoad: true
  validation: true
  customTemplates: []
```

## Exit Codes

- `0`: Success
- `1`: General error
- `2`: Invalid arguments
- `3`: Configuration error
- `4`: Template error
- `5`: Generation error

## Environment Variables

- `ARCHITEKAI_CONFIG`: Path to configuration file
- `ARCHITEKAI_LOG_LEVEL`: Override log level
- `ARCHITEKAI_NO_COLORS`: Disable colored output
- `ARCHITEKAI_TEMPLATES_DIR`: Override templates directory

## Error Handling

ArchitekAI provides comprehensive error handling with detailed error messages and suggestions for resolution.

### Common Error Scenarios

1. **Template Not Found**: Occurs when specified template doesn't exist
2. **Low Confidence**: When NLP parsing confidence is below threshold
3. **Invalid Format**: When unsupported output format is specified
4. **File Conflicts**: When output file already exists without --overwrite
5. **Configuration Errors**: When configuration file is invalid

### Debugging

Enable debug logging for troubleshooting:

```bash
# Environment variable
export LOG_LEVEL=debug
architek-ai generate "my architecture"

# Command line option
architek-ai generate "my architecture" --verbose

# Configuration
architek-ai config set --log-level debug
```
