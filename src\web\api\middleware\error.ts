/**
 * Error Handling Middleware for ArchitekAI Web API
 * 
 * Provides centralized error handling and 404 responses.
 */

import { Request, Response, NextFunction } from 'express';
import { createLogger } from '@/utils/logger';

const logger = createLogger('ErrorMiddleware');

/**
 * Global error handler middleware
 */
export function errorHandler(
  error: any,
  req: Request,
  res: Response,
  _next: NextFunction
): void {
  // Log the error
  logger.error('API Error:', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    },
  });

  // Don't expose error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  // Handle specific error types
  if (error.name === 'ValidationError') {
    res.status(400).json({
      error: 'Validation failed',
      code: 'VALIDATION_ERROR',
      details: isDevelopment ? error.details : undefined,
      message: error.message,
    });
    return;
  }

  if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
    res.status(400).json({
      error: 'Invalid JSON in request body',
      code: 'INVALID_JSON',
      message: 'Request body must be valid JSON',
    });
    return;
  }

  if (error.code === 'ENOENT') {
    res.status(404).json({
      error: 'Resource not found',
      code: 'RESOURCE_NOT_FOUND',
      message: 'The requested resource could not be found',
    });
    return;
  }

  if (error.code === 'EACCES') {
    res.status(403).json({
      error: 'Access denied',
      code: 'ACCESS_DENIED',
      message: 'Insufficient permissions to access the resource',
    });
    return;
  }

  if (error.code === 'EMFILE' || error.code === 'ENFILE') {
    res.status(503).json({
      error: 'Service temporarily unavailable',
      code: 'SERVICE_UNAVAILABLE',
      message: 'Too many open files. Please try again later.',
    });
    return;
  }

  if (error.name === 'TimeoutError') {
    res.status(408).json({
      error: 'Request timeout',
      code: 'REQUEST_TIMEOUT',
      message: 'The request took too long to process',
    });
    return;
  }

  if (error.name === 'PayloadTooLargeError') {
    res.status(413).json({
      error: 'Payload too large',
      code: 'PAYLOAD_TOO_LARGE',
      message: 'Request body exceeds maximum allowed size',
    });
    return;
  }

  // Handle rate limiting errors
  if (error.status === 429) {
    res.status(429).json({
      error: 'Too many requests',
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: error.retryAfter,
    });
    return;
  }

  // Handle authentication errors
  if (error.name === 'UnauthorizedError' || error.status === 401) {
    res.status(401).json({
      error: 'Unauthorized',
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    });
    return;
  }

  // Handle authorization errors
  if (error.name === 'ForbiddenError' || error.status === 403) {
    res.status(403).json({
      error: 'Forbidden',
      code: 'FORBIDDEN',
      message: 'Insufficient permissions',
    });
    return;
  }

  // Handle NLP parsing errors
  if (error.message && error.message.includes('NLP parsing failed')) {
    res.status(422).json({
      error: 'Unable to parse description',
      code: 'NLP_PARSING_FAILED',
      message: 'The provided description could not be understood. Please provide a clearer description.',
      suggestions: [
        'Use specific component types (e.g., "API gateway", "database")',
        'Describe relationships between components',
        'Include common architecture terminology',
      ],
    });
    return;
  }

  // Handle template errors
  if (error.message && error.message.includes('Template')) {
    res.status(400).json({
      error: 'Template error',
      code: 'TEMPLATE_ERROR',
      message: error.message,
    });
    return;
  }

  // Handle configuration errors
  if (error.message && error.message.includes('Configuration')) {
    res.status(500).json({
      error: 'Configuration error',
      code: 'CONFIGURATION_ERROR',
      message: 'Server configuration error. Please contact support.',
    });
    return;
  }

  // Default error response
  const statusCode = error.status || error.statusCode || 500;
  
  res.status(statusCode).json({
    error: statusCode >= 500 ? 'Internal server error' : 'Request failed',
    code: 'INTERNAL_ERROR',
    message: isDevelopment ? error.message : 'An unexpected error occurred',
    ...(isDevelopment && {
      stack: error.stack,
      details: error,
    }),
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || 'unknown',
  });
}

/**
 * 404 Not Found handler middleware
 */
export function notFoundHandler(req: Request, res: Response): void {
  logger.warn('404 Not Found:', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  res.status(404).json({
    error: 'Not found',
    code: 'NOT_FOUND',
    message: `The requested endpoint ${req.method} ${req.path} was not found`,
    availableEndpoints: {
      api: '/api/docs',
      health: '/health',
      generate: '/api/generate',
      templates: '/api/templates',
      config: '/api/config',
    },
    timestamp: new Date().toISOString(),
  });
}

/**
 * Async error wrapper for route handlers
 */
export function asyncHandler(
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Create custom error with status code
 */
export class APIError extends Error {
  public status: number;
  public code: string;
  public details?: any;

  constructor(message: string, status: number = 500, code: string = 'API_ERROR', details?: any) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

/**
 * Create validation error
 */
export class ValidationError extends APIError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
    this.name = 'ValidationError';
  }
}

/**
 * Create not found error
 */
export class NotFoundError extends APIError {
  constructor(resource: string) {
    super(`${resource} not found`, 404, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

/**
 * Create unauthorized error
 */
export class UnauthorizedError extends APIError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, 'UNAUTHORIZED');
    this.name = 'UnauthorizedError';
  }
}

/**
 * Create forbidden error
 */
export class ForbiddenError extends APIError {
  constructor(message: string = 'Forbidden') {
    super(message, 403, 'FORBIDDEN');
    this.name = 'ForbiddenError';
  }
}

/**
 * Create conflict error
 */
export class ConflictError extends APIError {
  constructor(message: string) {
    super(message, 409, 'CONFLICT');
    this.name = 'ConflictError';
  }
}

/**
 * Create rate limit error
 */
export class RateLimitError extends APIError {
  public retryAfter: number;

  constructor(message: string = 'Rate limit exceeded', retryAfter: number = 60) {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
  }
}
