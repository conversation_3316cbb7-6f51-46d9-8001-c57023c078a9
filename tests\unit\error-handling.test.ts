/**
 * Tests for error handling in conversion functionality
 */

import { MermaidConverter } from '../../src/core/converter';
import fs from 'fs-extra';
import path from 'path';
import os from 'os';

// <PERSON><PERSON> Puppeteer to simulate various error conditions
jest.mock('puppeteer', () => ({
  launch: jest.fn(),
}));

describe('Error Handling', () => {
  let tempDir: string;

  beforeAll(async () => {
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'architek-ai-error-test-'));
  });

  afterAll(async () => {
    await fs.remove(tempDir);
  });

  describe('MermaidConverter Error Scenarios', () => {
    let converter: MermaidConverter;

    beforeEach(() => {
      converter = new MermaidConverter();
    });

    afterEach(async () => {
      if (converter) {
        await converter.cleanup();
      }
    });

    it('should handle browser launch failure', async () => {
      const puppeteer = require('puppeteer');
      puppeteer.launch.mockRejectedValueOnce(new Error('Failed to launch browser'));

      await expect(converter.initialize()).rejects.toThrow('Converter initialization failed');
    });

    it('should handle browser crash during conversion', async () => {
      const mockBrowser = {
        newPage: jest.fn().mockRejectedValue(new Error('Browser crashed')),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const puppeteer = require('puppeteer');
      puppeteer.launch.mockResolvedValueOnce(mockBrowser);

      await converter.initialize();

      const testFile = path.join(tempDir, 'test.mmd');
      await fs.writeFile(testFile, 'graph TD\nA --> B');

      const result = await converter.convertFile(testFile, {
        format: 'jpg',
        overwrite: true,
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Browser crashed');
    });

    it('should handle page timeout', async () => {
      const mockPage = {
        setViewport: jest.fn().mockResolvedValue(undefined),
        setContent: jest.fn().mockResolvedValue(undefined),
        waitForSelector: jest.fn().mockRejectedValue(new Error('Timeout waiting for selector')),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const mockBrowser = {
        newPage: jest.fn().mockResolvedValue(mockPage),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const puppeteer = require('puppeteer');
      puppeteer.launch.mockResolvedValueOnce(mockBrowser);

      await converter.initialize();

      const testFile = path.join(tempDir, 'test.mmd');
      await fs.writeFile(testFile, 'graph TD\nA --> B');

      const result = await converter.convertFile(testFile, {
        format: 'jpg',
        overwrite: true,
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Timeout');
    });

    it('should handle invalid Mermaid syntax gracefully', async () => {
      const mockSvgElement = {
        boundingBox: jest.fn().mockResolvedValue({ width: 800, height: 600 }),
        screenshot: jest.fn().mockResolvedValue(Buffer.from('mock-image')),
      };

      const mockPage = {
        setViewport: jest.fn().mockResolvedValue(undefined),
        setContent: jest.fn().mockResolvedValue(undefined),
        waitForSelector: jest.fn().mockResolvedValue(undefined),
        $: jest.fn().mockResolvedValue(mockSvgElement),
        evaluate: jest.fn().mockResolvedValue('<svg>mock</svg>'),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const mockBrowser = {
        newPage: jest.fn().mockResolvedValue(mockPage),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const puppeteer = require('puppeteer');
      puppeteer.launch.mockResolvedValueOnce(mockBrowser);

      await converter.initialize();

      const testFile = path.join(tempDir, 'invalid.mmd');
      await fs.writeFile(testFile, 'invalid mermaid syntax here');

      const result = await converter.convertFile(testFile, {
        format: 'jpg',
        overwrite: true,
      });

      // Should handle gracefully - either succeed with error diagram or fail gracefully
      expect(typeof result.success).toBe('boolean');
      if (!result.success) {
        expect(result.error).toBeDefined();
      }
    });

    it('should handle file system errors', async () => {
      const testFile = '/invalid/path/test.mmd';

      const result = await converter.convertFile(testFile, {
        format: 'jpg',
        overwrite: true,
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });

    it('should handle permission errors', async () => {
      // Create a file and make it unreadable (if possible on the platform)
      const testFile = path.join(tempDir, 'readonly.mmd');
      await fs.writeFile(testFile, 'graph TD\nA --> B');

      try {
        await fs.chmod(testFile, 0o000); // Remove all permissions
        
        const result = await converter.convertFile(testFile, {
          format: 'jpg',
          overwrite: true,
        });

        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
      } catch (error) {
        // If chmod fails (e.g., on Windows), skip this test
        console.warn('Skipping permission test due to platform limitations');
      } finally {
        // Restore permissions for cleanup
        try {
          await fs.chmod(testFile, 0o644);
        } catch (error) {
          // Ignore cleanup errors
        }
      }
    });

    it('should handle output directory creation failure', async () => {
      const testFile = path.join(tempDir, 'test.mmd');
      await fs.writeFile(testFile, 'graph TD\nA --> B');

      // Try to write to an invalid output directory
      const result = await converter.convertFile(testFile, {
        format: 'jpg',
        outputDir: '/invalid/output/path',
        overwrite: true,
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle memory exhaustion gracefully', async () => {
      // This test simulates memory issues by creating a very large diagram
      const largeContent = 'graph TD\n' + Array.from({ length: 10000 }, (_, i) => 
        `A${i} --> B${i}`
      ).join('\n');

      const testFile = path.join(tempDir, 'large.mmd');
      await fs.writeFile(testFile, largeContent);

      const mockPage = {
        setViewport: jest.fn().mockResolvedValue(undefined),
        setContent: jest.fn().mockRejectedValue(new Error('Out of memory')),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const mockBrowser = {
        newPage: jest.fn().mockResolvedValue(mockPage),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const puppeteer = require('puppeteer');
      puppeteer.launch.mockResolvedValueOnce(mockBrowser);

      await converter.initialize();

      const result = await converter.convertFile(testFile, {
        format: 'jpg',
        overwrite: true,
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('memory');
    });
  });

  describe('Batch Conversion Error Handling', () => {
    let converter: MermaidConverter;

    beforeEach(async () => {
      converter = new MermaidConverter();
      
      const mockSvgElement = {
        boundingBox: jest.fn().mockResolvedValue({ width: 800, height: 600 }),
        screenshot: jest.fn().mockResolvedValue(Buffer.from('mock-image')),
      };

      const mockPage = {
        setViewport: jest.fn().mockResolvedValue(undefined),
        setContent: jest.fn().mockResolvedValue(undefined),
        waitForSelector: jest.fn().mockResolvedValue(undefined),
        $: jest.fn().mockResolvedValue(mockSvgElement),
        evaluate: jest.fn().mockResolvedValue('<svg>mock</svg>'),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const mockBrowser = {
        newPage: jest.fn().mockResolvedValue(mockPage),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const puppeteer = require('puppeteer');
      puppeteer.launch.mockResolvedValue(mockBrowser);

      await converter.initialize();
    });

    afterEach(async () => {
      await converter.cleanup();
    });

    it('should handle mixed success and failure in batch', async () => {
      // Create test files - some valid, some invalid
      const validFile1 = path.join(tempDir, 'valid1.mmd');
      const validFile2 = path.join(tempDir, 'valid2.mmd');
      const invalidFile = path.join(tempDir, 'nonexistent.mmd');

      await fs.writeFile(validFile1, 'graph TD\nA --> B');
      await fs.writeFile(validFile2, 'graph TD\nC --> D');
      // Don't create invalidFile

      const result = await converter.convertBatch([validFile1, invalidFile, validFile2], {
        format: 'jpg',
        overwrite: true,
      });

      expect(result.totalProcessed).toBe(3);
      expect(result.successful).toBe(2);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].file).toBe(invalidFile);
    });

    it('should continue processing after individual failures', async () => {
      const files = [];
      for (let i = 0; i < 5; i++) {
        const file = path.join(tempDir, `batch${i}.mmd`);
        if (i === 2) {
          // Make one file invalid
          files.push(path.join(tempDir, 'nonexistent.mmd'));
        } else {
          await fs.writeFile(file, `graph TD\nA${i} --> B${i}`);
          files.push(file);
        }
      }

      const result = await converter.convertBatch(files, {
        format: 'png',
        overwrite: true,
      });

      expect(result.totalProcessed).toBe(5);
      expect(result.successful).toBe(4);
      expect(result.failed).toBe(1);
    });
  });

  describe('Resource Cleanup', () => {
    it('should cleanup resources even after errors', async () => {
      const converter = new MermaidConverter();
      
      const mockBrowser = {
        newPage: jest.fn().mockRejectedValue(new Error('Page creation failed')),
        close: jest.fn().mockResolvedValue(undefined),
      };

      const puppeteer = require('puppeteer');
      puppeteer.launch.mockResolvedValueOnce(mockBrowser);

      await converter.initialize();

      const testFile = path.join(tempDir, 'test.mmd');
      await fs.writeFile(testFile, 'graph TD\nA --> B');

      // This should fail but not crash
      await converter.convertFile(testFile, {
        format: 'jpg',
        overwrite: true,
      });

      // Cleanup should still work
      await expect(converter.cleanup()).resolves.not.toThrow();
      expect(mockBrowser.close).toHaveBeenCalled();
    });

    it('should handle cleanup errors gracefully', async () => {
      const converter = new MermaidConverter();
      
      const mockBrowser = {
        newPage: jest.fn().mockResolvedValue({
          setViewport: jest.fn().mockResolvedValue(undefined),
          close: jest.fn().mockResolvedValue(undefined),
        }),
        close: jest.fn().mockRejectedValue(new Error('Browser close failed')),
      };

      const puppeteer = require('puppeteer');
      puppeteer.launch.mockResolvedValueOnce(mockBrowser);

      await converter.initialize();

      // Cleanup should not throw even if browser.close() fails
      await expect(converter.cleanup()).resolves.not.toThrow();
    });
  });
});
