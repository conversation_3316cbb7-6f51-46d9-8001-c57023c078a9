/**
 * Conversion API Routes for ArchitekAI
 * 
 * Handles Mermaid diagram conversion requests via REST API.
 */

import { Router, Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs-extra';
import { MermaidConverter, ConversionOptions } from '@/core/converter';
import { createLogger } from '@/utils/logger';

const logger = createLogger('ConvertRoutes');

// Configure multer for file uploads
const upload = multer({
  dest: 'tmp/uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (path.extname(file.originalname).toLowerCase() === '.mmd') {
      cb(null, true);
    } else {
      cb(new Error('Only .mmd files are allowed'));
    }
  },
});

interface ConvertRequest {
  content?: string;
  format: 'jpg' | 'jpeg' | 'png' | 'svg';
  options?: {
    quality?: number;
    width?: number;
    height?: number;
    scale?: number;
    backgroundColor?: string;
    theme?: 'default' | 'dark' | 'forest' | 'neutral';
  };
}

interface PreviewRequest {
  content: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
}

export function createConvertRoutes(): Router {
  const router = Router();

  /**
   * POST /api/convert
   * Convert Mermaid content to image format
   */
  router.post('/', async (req: Request, res: Response, next: NextFunction) => {
    let converter: MermaidConverter | null = null;
    let tempFilePath: string | null = null;

    try {
      const { content, format, options = {} }: ConvertRequest = req.body;

      // Validate request
      if (!content || typeof content !== 'string' || content.trim().length === 0) {
        return res.status(400).json({
          error: 'Mermaid content is required',
          code: 'INVALID_CONTENT',
        });
      }

      if (!format || !['jpg', 'jpeg', 'png', 'svg'].includes(format)) {
        return res.status(400).json({
          error: 'Invalid format. Supported formats: jpg, jpeg, png, svg',
          code: 'INVALID_FORMAT',
        });
      }

      // Create temporary file
      tempFilePath = await createTempMermaidFile(content);

      // Initialize converter
      converter = new MermaidConverter();
      await converter.initialize();

      // Prepare conversion options
      const conversionOptions: ConversionOptions = {
        format,
        quality: options.quality || 90,
        width: options.width || 1920,
        height: options.height || 1080,
        scale: options.scale || 1,
        backgroundColor: options.backgroundColor || '#ffffff',
        theme: options.theme || 'default',
        overwrite: true,
      };

      // Convert the diagram
      const result = await converter.convertFile(tempFilePath, conversionOptions);

      if (!result.success) {
        return res.status(500).json({
          error: result.error || 'Conversion failed',
          code: 'CONVERSION_FAILED',
        });
      }

      // Read the converted file
      const imageBuffer = await fs.readFile(result.outputFile);

      // Set appropriate headers
      const mimeType = format === 'svg' ? 'image/svg+xml' : `image/${format}`;
      res.set({
        'Content-Type': mimeType,
        'Content-Length': imageBuffer.length.toString(),
        'Content-Disposition': `attachment; filename="diagram.${format}"`,
      });

      // Send the image
      res.send(imageBuffer);

      // Cleanup
      await fs.remove(result.outputFile);

    } catch (error) {
      logger.error('Conversion failed:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      res.status(500).json({
        error: errorMessage,
        code: 'INTERNAL_ERROR',
      });
    } finally {
      // Cleanup resources
      if (converter) {
        await converter.cleanup();
      }
      if (tempFilePath) {
        await cleanupTempFile(tempFilePath);
      }
    }
  });

  /**
   * POST /api/convert/upload
   * Convert uploaded .mmd file to image format
   */
  router.post('/upload', upload.single('file'), async (req: Request, res: Response, next: NextFunction) => {
    let converter: MermaidConverter | null = null;

    try {
      if (!req.file) {
        return res.status(400).json({
          error: 'No file uploaded',
          code: 'NO_FILE',
        });
      }

      const { format = 'jpg', quality = 90, width = 1920, height = 1080, theme = 'default' } = req.body;

      if (!['jpg', 'jpeg', 'png', 'svg'].includes(format)) {
        return res.status(400).json({
          error: 'Invalid format. Supported formats: jpg, jpeg, png, svg',
          code: 'INVALID_FORMAT',
        });
      }

      // Initialize converter
      converter = new MermaidConverter();
      await converter.initialize();

      // Prepare conversion options
      const conversionOptions: ConversionOptions = {
        format,
        quality: parseInt(quality),
        width: parseInt(width),
        height: parseInt(height),
        theme,
        backgroundColor: '#ffffff',
        overwrite: true,
      };

      // Convert the uploaded file
      const result = await converter.convertFile(req.file.path, conversionOptions);

      if (!result.success) {
        return res.status(500).json({
          error: result.error || 'Conversion failed',
          code: 'CONVERSION_FAILED',
        });
      }

      // Read the converted file
      const imageBuffer = await fs.readFile(result.outputFile);

      // Set appropriate headers
      const mimeType = format === 'svg' ? 'image/svg+xml' : `image/${format}`;
      res.set({
        'Content-Type': mimeType,
        'Content-Length': imageBuffer.length.toString(),
        'Content-Disposition': `attachment; filename="${path.basename(req.file.originalname, '.mmd')}.${format}"`,
      });

      // Send the image
      res.send(imageBuffer);

      // Cleanup
      await fs.remove(result.outputFile);

    } catch (error) {
      logger.error('File conversion failed:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      res.status(500).json({
        error: errorMessage,
        code: 'INTERNAL_ERROR',
      });
    } finally {
      // Cleanup resources
      if (converter) {
        await converter.cleanup();
      }
      if (req.file) {
        await cleanupTempFile(req.file.path);
      }
    }
  });

  /**
   * POST /api/convert/preview
   * Generate SVG preview of Mermaid content
   */
  router.post('/preview', async (req: Request, res: Response, next: NextFunction) => {
    let converter: MermaidConverter | null = null;
    let tempFilePath: string | null = null;

    try {
      const { content, theme = 'default' }: PreviewRequest = req.body;

      // Validate request
      if (!content || typeof content !== 'string' || content.trim().length === 0) {
        return res.status(400).json({
          error: 'Mermaid content is required',
          code: 'INVALID_CONTENT',
        });
      }

      // Create temporary file
      tempFilePath = await createTempMermaidFile(content);

      // Initialize converter
      converter = new MermaidConverter();
      await converter.initialize();

      // Convert to SVG for preview
      const result = await converter.convertFile(tempFilePath, {
        format: 'svg',
        theme,
        backgroundColor: 'transparent',
        overwrite: true,
      });

      if (!result.success) {
        return res.status(500).json({
          error: result.error || 'Preview generation failed',
          code: 'PREVIEW_FAILED',
        });
      }

      // Read the SVG content
      const svgContent = await fs.readFile(result.outputFile, 'utf8');

      res.json({
        svg: svgContent,
        width: result.width,
        height: result.height,
      });

      // Cleanup
      await fs.remove(result.outputFile);

    } catch (error) {
      logger.error('Preview generation failed:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      res.status(500).json({
        error: errorMessage,
        code: 'INTERNAL_ERROR',
      });
    } finally {
      // Cleanup resources
      if (converter) {
        await converter.cleanup();
      }
      if (tempFilePath) {
        await cleanupTempFile(tempFilePath);
      }
    }
  });

  return router;
}

// Helper functions
async function createTempMermaidFile(content: string): Promise<string> {
  const tempDir = 'tmp/mermaid';
  await fs.ensureDir(tempDir);
  
  const tempFileName = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}.mmd`;
  const tempFilePath = path.join(tempDir, tempFileName);
  
  await fs.writeFile(tempFilePath, content, 'utf8');
  return tempFilePath;
}

async function cleanupTempFile(filePath: string): Promise<void> {
  try {
    if (await fs.pathExists(filePath)) {
      await fs.remove(filePath);
    }
  } catch (error) {
    logger.warn(`Failed to cleanup temp file ${filePath}:`, error);
  }
}
