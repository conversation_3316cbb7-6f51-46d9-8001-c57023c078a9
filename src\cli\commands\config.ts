/**
 * Config Command for ArchitekAI CLI
 * 
 * Handles configuration management operations.
 */

import { Command } from 'commander';
import chalk from 'chalk';
// import inquirer from 'inquirer'; // TODO: Implement interactive config
import { ConfigManager } from '@/core/config/manager';
import { OutputFormat, LogLevel } from '@/types/config';
import { createLogger } from '@/utils/logger';
import { 
  createSpinner, 
  displaySuccess, 
  displayError, 
  displayInfo,
  displayTable,
  confirmAction,
} from '../utils';

const logger = createLogger('ConfigCommand');

interface ConfigSetOptions {
  outputFormat?: OutputFormat;
  outputDirectory?: string;
  logLevel?: LogLevel;
  interactive?: boolean;
  verbose?: boolean;
  colors?: boolean;
}

interface ConfigGetOptions {
  key?: string;
  format?: 'json' | 'yaml' | 'table';
  global?: boolean;
  local?: boolean;
}

export function configCommand(configManager: ConfigManager): Command {
  const command = new Command('config');

  command
    .description('Manage ArchitekAI configuration')
    .addHelpText('after', `
Examples:
  ${chalk.cyan('architekAI config show')}
  ${chalk.cyan('architekAI config set --output-format mermaid')}
  ${chalk.cyan('architekAI config set --output-directory ./diagrams')}
  ${chalk.cyan('architekAI config get output.defaultFormat')}
  ${chalk.cyan('architekAI config reset')}
  ${chalk.cyan('architekAI config validate')}
    `);

  // Show config subcommand
  command
    .command('show')
    .description('Show current configuration')
    .option('-f, --format <format>', 'output format (json, yaml, table)', 'table')
    .option('--global', 'show global configuration only')
    .option('--local', 'show local configuration only')
    .action(async (options: ConfigGetOptions) => {
      await executeConfigShow(options, configManager);
    });

  // Get config value subcommand
  command
    .command('get <key>')
    .description('Get a specific configuration value')
    .option('-f, --format <format>', 'output format (json, yaml)', 'json')
    .action(async (key: string, options: ConfigGetOptions) => {
      await executeConfigGet(key, options, configManager);
    });

  // Set config subcommand
  command
    .command('set')
    .description('Set configuration values')
    .option('--output-format <format>', 'default output format')
    .option('--output-directory <path>', 'default output directory')
    .option('--log-level <level>', 'logging level')
    .option('--interactive <boolean>', 'enable interactive mode by default')
    .option('--verbose <boolean>', 'enable verbose output by default')
    .option('--colors <boolean>', 'enable colored output')
    .action(async (options: ConfigSetOptions) => {
      await executeConfigSet(options, configManager);
    });

  // Reset config subcommand
  command
    .command('reset')
    .description('Reset configuration to defaults')
    .option('--global', 'reset global configuration')
    .option('--local', 'reset local configuration')
    .option('--force', 'skip confirmation prompt')
    .action(async (options: { global?: boolean; local?: boolean; force?: boolean }) => {
      await executeConfigReset(options, configManager);
    });

  // Validate config subcommand
  command
    .command('validate')
    .description('Validate current configuration')
    .action(async () => {
      await executeConfigValidate(configManager);
    });

  // Edit config subcommand
  command
    .command('edit')
    .description('Open configuration file in default editor')
    .option('--global', 'edit global configuration')
    .option('--local', 'edit local configuration')
    .action(async (options: { global?: boolean; local?: boolean }) => {
      await executeConfigEdit(options, configManager);
    });

  return command;
}

async function executeConfigShow(options: ConfigGetOptions, configManager: ConfigManager): Promise<void> {
  try {
    const config = configManager.getConfig();
    
    displayInfo('Current ArchitekAI Configuration:');

    if (options.format === 'json') {
      console.log(JSON.stringify(config, null, 2));
    } else if (options.format === 'yaml') {
      const yaml = await import('yaml');
      console.log(yaml.stringify(config));
    } else {
      // Table format - show key configuration values
      const configData = [
        { Setting: 'Output Format', Value: config.output.defaultFormat },
        { Setting: 'Output Directory', Value: config.output.directory },
        { Setting: 'Log Level', Value: config.logging.level },
        { Setting: 'Interactive Mode', Value: config.cli.interactive ? 'enabled' : 'disabled' },
        { Setting: 'Colors', Value: config.cli.colors ? 'enabled' : 'disabled' },
        { Setting: 'Verbose', Value: config.cli.verbose ? 'enabled' : 'disabled' },
        { Setting: 'Web Server Port', Value: config.web.port.toString() },
        { Setting: 'Template Directory', Value: config.templates.directory },
      ];

      displayTable(configData);
    }

  } catch (error) {
    logger.error('Config show failed:', error);
    displayError(error instanceof Error ? error.message : 'Failed to show configuration');
    process.exit(1);
  }
}

async function executeConfigGet(key: string, options: ConfigGetOptions, configManager: ConfigManager): Promise<void> {
  try {
    const config = configManager.getConfig();
    const value = getNestedValue(config, key);

    if (value === undefined) {
      displayError(`Configuration key '${key}' not found`);
      process.exit(1);
    }

    if (options.format === 'yaml') {
      const yaml = await import('yaml');
      console.log(yaml.stringify({ [key]: value }));
    } else {
      console.log(JSON.stringify(value, null, 2));
    }

  } catch (error) {
    logger.error('Config get failed:', error);
    displayError(error instanceof Error ? error.message : 'Failed to get configuration value');
    process.exit(1);
  }
}

async function executeConfigSet(options: ConfigSetOptions, configManager: ConfigManager): Promise<void> {
  const spinner = createSpinner('Updating configuration...');
  
  try {
    spinner.start();

    const updates: Record<string, unknown> = {};

    // Build updates object
    if (options.outputFormat) {
      updates['output.defaultFormat'] = options.outputFormat;
    }
    if (options.outputDirectory) {
      updates['output.directory'] = options.outputDirectory;
    }
    if (options.logLevel) {
      updates['logging.level'] = options.logLevel;
    }
    if (options.interactive !== undefined) {
      updates['cli.interactive'] = options.interactive;
    }
    if (options.verbose !== undefined) {
      updates['cli.verbose'] = options.verbose;
    }
    if (options.colors !== undefined) {
      updates['cli.colors'] = options.colors;
    }

    if (Object.keys(updates).length === 0) {
      spinner.stop();
      displayError('No configuration values specified to update');
      process.exit(1);
    }

    // Apply updates
    for (const [key, value] of Object.entries(updates)) {
      await configManager.set(key, value);
    }

    await configManager.save();

    spinner.succeed('Configuration updated successfully!');

    // Show what was updated
    displaySuccess('Updated configuration:');
    for (const [key, value] of Object.entries(updates)) {
      console.log(`  ${chalk.cyan(key)}: ${chalk.yellow(String(value))}`);
    }

  } catch (error) {
    spinner.fail('Failed to update configuration');
    logger.error('Config set failed:', error);
    displayError(error instanceof Error ? error.message : 'Failed to update configuration');
    process.exit(1);
  }
}

async function executeConfigReset(
  options: { global?: boolean; local?: boolean; force?: boolean }, 
  configManager: ConfigManager
): Promise<void> {
  try {
    const scope = options.global ? 'global' : options.local ? 'local' : 'all';
    
    if (!options.force) {
      const confirmed = await confirmAction(
        `Are you sure you want to reset ${scope} configuration to defaults?`, 
        false
      );
      
      if (!confirmed) {
        displayInfo('Configuration reset cancelled.');
        return;
      }
    }

    const spinner = createSpinner(`Resetting ${scope} configuration...`);
    spinner.start();

    await configManager.reset(scope);

    spinner.succeed('Configuration reset successfully!');
    displaySuccess(`Reset ${scope} configuration to defaults`);

  } catch (error) {
    logger.error('Config reset failed:', error);
    displayError(error instanceof Error ? error.message : 'Failed to reset configuration');
    process.exit(1);
  }
}

async function executeConfigValidate(configManager: ConfigManager): Promise<void> {
  const spinner = createSpinner('Validating configuration...');
  
  try {
    spinner.start();

    const validation = await configManager.validate();

    spinner.stop();

    if (validation.isValid) {
      displaySuccess('Configuration is valid!');
    } else {
      displayError('Configuration validation failed:');
      
      validation.errors.forEach(error => {
        console.log(chalk.red(`  ❌ ${error.path}: ${error.message}`));
      });

      if (validation.warnings.length > 0) {
        console.log(chalk.yellow('\nWarnings:'));
        validation.warnings.forEach(warning => {
          console.log(chalk.yellow(`  ⚠️  ${warning.path}: ${warning.message}`));
          if (warning.suggestion) {
            console.log(chalk.gray(`     Suggestion: ${warning.suggestion}`));
          }
        });
      }

      process.exit(1);
    }

  } catch (error) {
    spinner.fail('Configuration validation failed');
    logger.error('Config validate failed:', error);
    displayError(error instanceof Error ? error.message : 'Failed to validate configuration');
    process.exit(1);
  }
}

async function executeConfigEdit(
  options: { global?: boolean; local?: boolean }, 
  configManager: ConfigManager
): Promise<void> {
  try {
    const configPath = options.global 
      ? configManager.getGlobalConfigPath()
      : options.local 
        ? configManager.getLocalConfigPath()
        : configManager.getActiveConfigPath();

    displayInfo(`Opening configuration file: ${configPath}`);

    // Try to open with default editor
    const { spawn } = await import('child_process');
    const editor = process.env.EDITOR || process.env.VISUAL || 'notepad';
    
    const child = spawn(editor, [configPath], {
      stdio: 'inherit',
      detached: true,
    });

    child.on('error', (error) => {
      displayError(`Failed to open editor: ${error.message}`);
      displayInfo(`Please manually edit: ${configPath}`);
    });

  } catch (error) {
    logger.error('Config edit failed:', error);
    displayError(error instanceof Error ? error.message : 'Failed to open configuration editor');
    process.exit(1);
  }
}

// Helper functions

function getNestedValue(obj: any, path: string): unknown {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// Mock implementation for ConfigManager methods that don't exist yet
declare module '@/core/config/manager' {
  interface ConfigManager {
    set(key: string, value: unknown): Promise<void>;
    save(): Promise<void>;
    reset(scope?: 'global' | 'local' | 'user' | 'all'): Promise<void>;
    validate(): Promise<{ isValid: boolean; errors: any[]; warnings: any[] }>;
    getGlobalConfigPath(): string;
    getLocalConfigPath(): string;
    getActiveConfigPath(): string;
  }
}
