<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ArchitekAI - Intelligent Architecture Diagram Generator</title>
    <meta name="description" content="Generate beautiful architecture diagrams from natural language descriptions using AI-powered analysis." />
    <meta name="keywords" content="architecture, diagrams, AI, microservices, system design, mermaid, plantuml" />
    <meta name="author" content="ArchitekAI Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://architekAI.dev/" />
    <meta property="og:title" content="ArchitekAI - Intelligent Architecture Diagram Generator" />
    <meta property="og:description" content="Generate beautiful architecture diagrams from natural language descriptions using AI-powered analysis." />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://architekAI.dev/" />
    <meta property="twitter:title" content="ArchitekAI - Intelligent Architecture Diagram Generator" />
    <meta property="twitter:description" content="Generate beautiful architecture diagrams from natural language descriptions using AI-powered analysis." />
    <meta property="twitter:image" content="/twitter-image.png" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Theme color -->
    <meta name="theme-color" content="#2563eb" />
    
    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    
    <!-- Styles -->
    <style>
      /* Loading screen styles */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-screen.fade-out {
        opacity: 0;
        pointer-events: none;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 24px;
        animation: pulse 2s infinite;
      }
      
      .loading-text {
        color: white;
        font-family: 'Inter', sans-serif;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      
      .loading-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 400;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-top: 24px;
      }
      
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide scrollbar during loading */
      body.loading {
        overflow: hidden;
      }
    </style>
  </head>
  <body class="loading">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
      <svg class="loading-logo" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="10" y="10" width="80" height="80" rx="12" fill="white" fill-opacity="0.1" stroke="white" stroke-width="2"/>
        <rect x="20" y="20" width="25" height="15" rx="3" fill="white"/>
        <rect x="55" y="20" width="25" height="15" rx="3" fill="white"/>
        <rect x="20" y="45" width="60" height="10" rx="2" fill="white" fill-opacity="0.7"/>
        <rect x="20" y="65" width="25" height="15" rx="3" fill="white"/>
        <rect x="55" y="65" width="25" height="15" rx="3" fill="white"/>
        <path d="M32.5 35L47.5 50L32.5 65" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M67.5 35L52.5 50L67.5 65" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <div class="loading-text">ArchitekAI</div>
      <div class="loading-subtitle">Intelligent Architecture Diagram Generator</div>
      <div class="loading-spinner"></div>
    </div>

    <!-- React App Root -->
    <div id="root"></div>
    
    <!-- Scripts -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Loading screen removal script -->
    <script>
      // Remove loading screen when React app is ready
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          const body = document.body;
          
          if (loadingScreen) {
            loadingScreen.classList.add('fade-out');
            body.classList.remove('loading');
            
            setTimeout(function() {
              loadingScreen.remove();
            }, 500);
          }
        }, 1000); // Minimum loading time for better UX
      });
    </script>
  </body>
</html>
