/**
 * Core architecture types for ArchitekAI
 */

// Re-export OutputFormat from config types for convenience
export { OutputFormat } from './config';

export interface Component {
  id: string;
  name: string;
  type: ComponentType;
  description?: string;
  properties?: Record<string, unknown>;
  position?: Position;
  metadata?: ComponentMetadata;
}

export interface Connection {
  id: string;
  source: string;
  target: string;
  type: ConnectionType;
  label?: string;
  properties?: Record<string, unknown>;
  bidirectional?: boolean;
}

export interface Architecture {
  id: string;
  name: string;
  description: string;
  components: Component[];
  connections: Connection[];
  metadata: ArchitectureMetadata;
  template?: string;
}

export interface Position {
  x: number;
  y: number;
  z?: number;
}

export interface ComponentMetadata {
  tags?: string[];
  category?: string;
  technology?: string;
  version?: string;
  criticality?: 'low' | 'medium' | 'high' | 'critical';
  scalability?: ScalabilityInfo;
}

export interface ArchitectureMetadata {
  createdAt: Date;
  updatedAt: Date;
  version: string;
  author?: string;
  template?: string;
  style?: StyleConfig;
  validation?: ValidationResult;
}

export interface ScalabilityInfo {
  horizontal: boolean;
  vertical: boolean;
  autoScaling?: boolean;
  maxInstances?: number;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  code: string;
  message: string;
  component?: string;
  severity: 'error' | 'warning' | 'info';
}

export interface ValidationWarning extends ValidationError {
  suggestion?: string;
}

export interface StyleConfig {
  theme: 'light' | 'dark' | 'auto';
  colors: ColorScheme;
  fonts: FontConfig;
  layout: LayoutConfig;
}

export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}

export interface FontConfig {
  family: string;
  size: {
    small: number;
    medium: number;
    large: number;
  };
  weight: {
    normal: number;
    bold: number;
  };
}

export interface LayoutConfig {
  direction: 'horizontal' | 'vertical' | 'auto';
  spacing: {
    component: number;
    connection: number;
    group: number;
  };
  alignment: 'start' | 'center' | 'end';
}

export enum ComponentType {
  SERVICE = 'service',
  DATABASE = 'database',
  API_GATEWAY = 'api_gateway',
  LOAD_BALANCER = 'load_balancer',
  CACHE = 'cache',
  QUEUE = 'queue',
  EVENT_STREAM = 'event_stream',
  STORAGE = 'storage',
  CDN = 'cdn',
  FUNCTION = 'function',
  CONTAINER = 'container',
  EXTERNAL_SERVICE = 'external_service',
  USER_INTERFACE = 'user_interface',
  MOBILE_APP = 'mobile_app',
  WEB_APP = 'web_app',
  DESKTOP_APP = 'desktop_app',
  IOT_DEVICE = 'iot_device',
  NETWORK = 'network',
  SECURITY = 'security',
  MONITORING = 'monitoring',
  LOGGING = 'logging',
  ANALYTICS = 'analytics',
  NOTIFICATION = 'notification',
  WORKFLOW = 'workflow',
  DATA_PIPELINE = 'data_pipeline',
  ML_MODEL = 'ml_model',
  CUSTOM = 'custom',
}

export enum ConnectionType {
  HTTP = 'http',
  HTTPS = 'https',
  TCP = 'tcp',
  UDP = 'udp',
  WEBSOCKET = 'websocket',
  GRPC = 'grpc',
  GRAPHQL = 'graphql',
  REST = 'rest',
  SOAP = 'soap',
  MESSAGE_QUEUE = 'message_queue',
  EVENT_STREAM = 'event_stream',
  DATABASE_CONNECTION = 'database_connection',
  FILE_SYSTEM = 'file_system',
  NETWORK = 'network',
  VPN = 'vpn',
  API_CALL = 'api_call',
  DEPENDENCY = 'dependency',
  DATA_FLOW = 'data_flow',
  CONTROL_FLOW = 'control_flow',
  INHERITANCE = 'inheritance',
  COMPOSITION = 'composition',
  AGGREGATION = 'aggregation',
  ASSOCIATION = 'association',
  CUSTOM = 'custom',
}

export enum ArchitecturePattern {
  MICROSERVICES = 'microservices',
  MONOLITHIC = 'monolithic',
  SERVERLESS = 'serverless',
  EVENT_DRIVEN = 'event_driven',
  LAYERED = 'layered',
  HEXAGONAL = 'hexagonal',
  CLEAN_ARCHITECTURE = 'clean_architecture',
  MVC = 'mvc',
  MVP = 'mvp',
  MVVM = 'mvvm',
  PIPE_AND_FILTER = 'pipe_and_filter',
  BLACKBOARD = 'blackboard',
  BROKER = 'broker',
  PEER_TO_PEER = 'peer_to_peer',
  SERVICE_ORIENTED = 'service_oriented',
  SPACE_BASED = 'space_based',
  MASTER_SLAVE = 'master_slave',
  CUSTOM = 'custom',
}

export interface ArchitectureTemplate {
  id: string;
  name: string;
  description: string;
  pattern: ArchitecturePattern;
  components: Partial<Component>[];
  connections: Partial<Connection>[];
  metadata: TemplateMetadata;
  customization: CustomizationOptions;
}

export interface TemplateMetadata {
  version: string;
  author: string;
  tags: string[];
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  useCases: string[];
  prerequisites: string[];
  documentation: string;
}

export interface CustomizationOptions {
  allowComponentAddition: boolean;
  allowComponentRemoval: boolean;
  allowConnectionModification: boolean;
  requiredComponents: string[];
  optionalComponents: string[];
  configurationSchema: Record<string, unknown>;
}

export interface ParsedDescription {
  originalText: string;
  extractedComponents: ExtractedComponent[];
  extractedConnections: ExtractedConnection[];
  suggestedPattern: ArchitecturePattern;
  confidence: number;
  metadata: ParseMetadata;
  architecture: Architecture;
}

export interface ExtractedComponent {
  name: string;
  type: ComponentType;
  confidence: number;
  context: string[];
  properties: Record<string, unknown>;
}

export interface ExtractedConnection {
  source: string;
  target: string;
  type: ConnectionType;
  confidence: number;
  context: string[];
  properties: Record<string, unknown>;
}

export interface ParseMetadata {
  processingTime: number;
  nlpVersion: string;
  patterns: string[];
  keywords: string[];
  entities: NLPEntity[];
}

export interface NLPEntity {
  text: string;
  type: string;
  confidence: number;
  start: number;
  end: number;
}
