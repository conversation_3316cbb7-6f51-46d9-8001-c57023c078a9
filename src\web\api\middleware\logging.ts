/**
 * Logging Middleware for ArchitekAI Web API
 * 
 * Provides request logging and correlation IDs.
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { createLogger } from '@/utils/logger';

const logger = createLogger('RequestLogger');

/**
 * Request logging middleware
 */
export function requestLogger(req: Request, res: Response, next: NextFunction): void {
  const startTime = Date.now();
  
  // Generate or use existing request ID
  const requestId = req.headers['x-request-id'] as string || uuidv4();
  req.headers['x-request-id'] = requestId;
  res.setHeader('x-request-id', requestId);

  // Log request start
  logger.info('Request started', {
    requestId,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    contentLength: req.get('Content-Length'),
    contentType: req.get('Content-Type'),
  });

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const duration = Date.now() - startTime;

    // Log response
    logger.info('Request completed', {
      requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      contentLength: res.get('Content-Length'),
      contentType: res.get('Content-Type'),
    });

    // Call original end method and return the result
    return originalEnd.call(this, chunk, encoding);
  };

  next();
}

/**
 * Performance logging middleware
 */
export function performanceLogger(req: Request, res: Response, next: NextFunction): void {
  const startTime = process.hrtime.bigint();
  
  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    
    // Log slow requests
    if (duration > 1000) { // Log requests taking more than 1 second
      logger.warn('Slow request detected', {
        requestId: req.headers['x-request-id'],
        method: req.method,
        url: req.url,
        duration,
        statusCode: res.statusCode,
      });
    }
    
    // Log performance metrics
    logger.debug('Request performance', {
      requestId: req.headers['x-request-id'],
      method: req.method,
      url: req.url,
      duration,
      statusCode: res.statusCode,
      memoryUsage: process.memoryUsage(),
    });
  });

  next();
}

/**
 * Security logging middleware
 */
export function securityLogger(req: Request, res: Response, next: NextFunction): void {
  // Log suspicious activity
  const suspiciousPatterns = [
    /\.\./,  // Directory traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
  ];

  const url = req.url;
  const body = JSON.stringify(req.body);
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(url) || pattern.test(body)) {
      logger.warn('Suspicious request detected', {
        requestId: req.headers['x-request-id'],
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        pattern: pattern.toString(),
        body: req.body,
      });
      break;
    }
  }

  // Log failed authentication attempts
  res.on('finish', () => {
    if (res.statusCode === 401) {
      logger.warn('Authentication failed', {
        requestId: req.headers['x-request-id'],
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
    }
  });

  next();
}

/**
 * API usage logging middleware
 */
export function usageLogger(req: Request, res: Response, next: NextFunction): void {
  // Track API endpoint usage
  const endpoint = req.route?.path || req.path;
  const method = req.method;
  
  res.on('finish', () => {
    logger.info('API usage', {
      requestId: req.headers['x-request-id'],
      endpoint,
      method,
      statusCode: res.statusCode,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString(),
    });
  });

  next();
}

/**
 * Error logging middleware
 */
export function errorLogger(error: any, req: Request, _res: Response, next: NextFunction): void {
  logger.error('Request error', {
    requestId: req.headers['x-request-id'],
    method: req.method,
    url: req.url,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
  });

  next(error);
}

/**
 * Structured logging helper
 */
export class RequestLogger {
  private requestId: string;
  private logger: any;

  constructor(req: Request) {
    this.requestId = req.headers['x-request-id'] as string;
    this.logger = createLogger('Request');
  }

  info(message: string, meta?: any): void {
    this.logger.info(message, {
      requestId: this.requestId,
      ...meta,
    });
  }

  warn(message: string, meta?: any): void {
    this.logger.warn(message, {
      requestId: this.requestId,
      ...meta,
    });
  }

  error(message: string, meta?: any): void {
    this.logger.error(message, {
      requestId: this.requestId,
      ...meta,
    });
  }

  debug(message: string, meta?: any): void {
    this.logger.debug(message, {
      requestId: this.requestId,
      ...meta,
    });
  }
}

/**
 * Create request logger instance
 */
export function createRequestLogger(req: Request): RequestLogger {
  return new RequestLogger(req);
}
