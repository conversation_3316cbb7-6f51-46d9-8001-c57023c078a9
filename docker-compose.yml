version: '3.8'

services:
  # Architek-AI CLI service
  architek-ai-cli:
    build:
      context: .
      target: cli
    container_name: architek-ai-cli
    volumes:
      - ./diagrams:/app/diagrams
      - ./logs:/app/logs
      - ./config:/app/config
      - architek-ai-data:/home/<USER>/.architek-ai
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - ARCHITEKTI_OUTPUT_DIR=/app/diagrams
    profiles:
      - cli

  # Architek-AI Web API service
  architek-ai-api:
    build:
      context: .
      target: web
    container_name: architek-ai-api
    ports:
      - "3000:3000"
    volumes:
      - ./diagrams:/app/diagrams
      - ./logs:/app/logs
      - ./config:/app/config
      - architek-ai-data:/home/<USER>/.architek-ai
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=info
      - ARCHITEKTI_WEB_PORT=3000
      - ARCHITEKTI_WEB_HOST=0.0.0.0
      - ARCHITEKTI_OUTPUT_DIR=/app/diagrams
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    profiles:
      - web
      - full

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: architek-ai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - web
      - full
      - cache

  # PostgreSQL for data storage (optional)
  postgres:
    image: postgres:15-alpine
    container_name: architek-ai-postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    environment:
      - POSTGRES_DB=architek_ai
      - POSTGRES_USER=architek_ai
      - POSTGRES_PASSWORD=architek_ai_password
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U architek_ai -d architek_ai"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - database
      - full

  # Nginx reverse proxy (for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: architek-ai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - architek-ai-api
    profiles:
      - production
      - full

  # Development environment with hot reload
  architek-ai-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: architek-ai-dev
    ports:
      - "3000:3000"
      - "3001:3001"  # Frontend dev server
    volumes:
      - .:/app
      - /app/node_modules
      - ./diagrams:/app/diagrams
      - ./logs:/app/logs
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - CHOKIDAR_USEPOLLING=true
    command: npm run dev:web
    profiles:
      - development

volumes:
  architek-ai-data:
    driver: local
  redis-data:
    driver: local
  postgres-data:
    driver: local

networks:
  default:
    name: architek-ai-network
    driver: bridge
