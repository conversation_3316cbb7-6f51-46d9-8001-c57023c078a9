import React from 'react';

const DocsPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Documentation</h1>
          
          <div className="bg-white rounded-lg shadow-md p-8">
            <div className="prose max-w-none">
              <h2>Getting Started</h2>
              <p>
                ArchitekAI is an intelligent architecture diagram generator that transforms natural language 
                descriptions into professional architecture diagrams. Here's how to get started:
              </p>
              
              <h3>CLI Usage</h3>
              <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm mb-4">
                # Generate a diagram from description<br/>
                architek-ai generate "microservices with API gateway and user service"<br/><br/>
                
                # Use a specific template<br/>
                architek-ai generate "e-commerce platform" --template microservices<br/><br/>
                
                # Non-interactive mode<br/>
                architek-ai generate "web app with database" --no-interactive<br/><br/>
                
                # List available templates<br/>
                architek-ai template list
              </div>
              
              <h3>Supported Formats</h3>
              <ul>
                <li><strong>Mermaid</strong> - Modern diagram syntax, great for GitHub and documentation</li>
                <li><strong>PlantUML</strong> - Powerful UML diagrams with extensive customization</li>
                <li><strong>ASCII</strong> - Text-based diagrams for terminal and plain text</li>
                <li><strong>Draw.io</strong> - Visual diagrams for presentations and documentation</li>
              </ul>
              
              <h3>Architecture Patterns</h3>
              <p>ArchitekAI recognizes and can generate diagrams for various architecture patterns:</p>
              <ul>
                <li><strong>Microservices</strong> - Distributed services with API gateways and service discovery</li>
                <li><strong>Monolithic</strong> - Traditional layered applications with centralized data</li>
                <li><strong>Serverless</strong> - Event-driven functions with managed cloud services</li>
                <li><strong>Event-Driven</strong> - Message queues and event streaming architectures</li>
              </ul>
              
              <h3>Writing Good Descriptions</h3>
              <p>To get the best results, follow these guidelines when describing your architecture:</p>
              <ul>
                <li>Be specific about components (e.g., "API gateway", "user service", "PostgreSQL database")</li>
                <li>Mention relationships (e.g., "connects to", "sends data to", "authenticates with")</li>
                <li>Include data flow direction when relevant</li>
                <li>Specify technologies when important (e.g., "Redis cache", "RabbitMQ queue")</li>
              </ul>
              
              <h3>Examples</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold">Good Description:</h4>
                  <div className="bg-green-50 p-3 rounded text-sm">
                    "microservices architecture with nginx load balancer, API gateway for routing, 
                    user service connected to PostgreSQL database, order service with Redis cache, 
                    and RabbitMQ message queue for async communication"
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold">Basic Description:</h4>
                  <div className="bg-yellow-50 p-3 rounded text-sm">
                    "web application with frontend, backend, and database"
                  </div>
                </div>
              </div>
              
              <h3>Configuration</h3>
              <p>You can customize ArchitekAI's behavior using configuration files or command-line options:</p>
              <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm mb-4">
                # Set default output format<br/>
                architek-ai config set --output-format mermaid<br/><br/>
                
                # Configure output directory<br/>
                architek-ai config set --output-directory ./diagrams<br/><br/>
                
                # Enable verbose logging<br/>
                architek-ai config set --log-level debug
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocsPage;
