import React, { useState } from 'react';

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState({
    defaultFormat: 'mermaid',
    outputDirectory: './diagrams',
    logLevel: 'info',
    interactive: true,
    autoSave: true,
    theme: 'light'
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = () => {
    // TODO: Implement API call to save settings
    alert('Settings saved successfully!');
  };

  const handleReset = () => {
    setSettings({
      defaultFormat: 'mermaid',
      outputDirectory: './diagrams',
      logLevel: 'info',
      interactive: true,
      autoSave: true,
      theme: 'light'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Settings</h1>
          
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="space-y-6">
              {/* Output Settings */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Output Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Default Output Format
                    </label>
                    <select
                      value={settings.defaultFormat}
                      onChange={(e) => handleSettingChange('defaultFormat', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="mermaid">Mermaid</option>
                      <option value="plantuml">PlantUML</option>
                      <option value="ascii">ASCII</option>
                      <option value="drawio">Draw.io</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Output Directory
                    </label>
                    <input
                      type="text"
                      value={settings.outputDirectory}
                      onChange={(e) => handleSettingChange('outputDirectory', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
              
              {/* Behavior Settings */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Behavior Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Log Level
                    </label>
                    <select
                      value={settings.logLevel}
                      onChange={(e) => handleSettingChange('logLevel', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="error">Error</option>
                      <option value="warn">Warning</option>
                      <option value="info">Info</option>
                      <option value="debug">Debug</option>
                    </select>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="interactive"
                      checked={settings.interactive}
                      onChange={(e) => handleSettingChange('interactive', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="interactive" className="ml-2 block text-sm text-gray-700">
                      Enable interactive mode by default
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="autoSave"
                      checked={settings.autoSave}
                      onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="autoSave" className="ml-2 block text-sm text-gray-700">
                      Auto-save generated diagrams
                    </label>
                  </div>
                </div>
              </div>
              
              {/* UI Settings */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Interface Settings</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Theme
                    </label>
                    <select
                      value={settings.theme}
                      onChange={(e) => handleSettingChange('theme', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                      <option value="auto">Auto</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {/* Action Buttons */}
              <div className="flex gap-4 pt-6 border-t">
                <button
                  onClick={handleSave}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-semibold transition-colors"
                >
                  Save Settings
                </button>
                <button
                  onClick={handleReset}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded-md font-semibold transition-colors"
                >
                  Reset to Defaults
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
