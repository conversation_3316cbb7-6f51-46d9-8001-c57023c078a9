import React from 'react';
import { Link } from 'react-router-dom';

const Footer: React.FC = () => {
  return (
    <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">ArchitekAI</h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Intelligent architecture diagram generation powered by AI.
            </p>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Product</h4>
            <ul className="space-y-2 text-sm">
              <li><Link to="/generate" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">Generate</Link></li>
              <li><Link to="/templates" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">Templates</Link></li>
              <li><Link to="/examples" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">Examples</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Resources</h4>
            <ul className="space-y-2 text-sm">
              <li><Link to="/docs" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">Documentation</Link></li>
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">API Reference</a></li>
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">GitHub</a></li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Support</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">Help Center</a></li>
              <li><a href="#" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">Contact</a></li>
              <li><Link to="/settings" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">Settings</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center text-sm text-gray-600 dark:text-gray-400">
          <p>&copy; 2024 ArchitekAI. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
