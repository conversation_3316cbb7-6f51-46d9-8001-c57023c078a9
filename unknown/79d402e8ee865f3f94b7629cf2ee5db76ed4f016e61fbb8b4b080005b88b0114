import React from 'react';
import { Link } from 'react-router-dom';

const TemplatesPage: React.FC = () => {
  const templates = [
    {
      id: 'microservices',
      name: 'Microservices Architecture',
      description: 'Modern microservices architecture with API gateway, service discovery, and distributed data management',
      complexity: 'Moderate',
      components: 12,
      pattern: 'microservices'
    },
    {
      id: 'monolithic',
      name: 'Monolithic Architecture',
      description: 'Traditional monolithic architecture with layered design and centralized data management',
      complexity: 'Simple',
      components: 5,
      pattern: 'monolithic'
    },
    {
      id: 'serverless',
      name: 'Serverless Architecture',
      description: 'Event-driven serverless architecture with functions, queues, and managed services',
      complexity: 'Moderate',
      components: 10,
      pattern: 'serverless'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Architecture Templates</h1>
          <p className="text-gray-600 mb-8">
            Choose from our collection of pre-built architecture templates to get started quickly.
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.map((template) => (
              <div key={template.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{template.name}</h3>
                <p className="text-gray-600 mb-4 text-sm">{template.description}</p>
                
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Pattern:</span>
                    <span className="font-medium">{template.pattern}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Complexity:</span>
                    <span className="font-medium">{template.complexity}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Components:</span>
                    <span className="font-medium">{template.components}</span>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Link
                    to={`/templates/${template.id}`}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-md text-sm font-semibold transition-colors"
                  >
                    View Details
                  </Link>
                  <Link
                    to={`/generate?template=${template.id}`}
                    className="flex-1 bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-md text-sm font-semibold transition-colors"
                  >
                    Use Template
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplatesPage;
