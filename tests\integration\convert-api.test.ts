/**
 * Integration tests for Convert API endpoints
 */

import request from 'supertest';
import { ArchitekAIServer } from '../../src/web/api/server';
import fs from 'fs-extra';
import path from 'path';
import os from 'os';

// Mock the MermaidConverter to avoid browser dependencies in tests
jest.mock('../../src/core/converter', () => ({
  MermaidConverter: jest.fn().mockImplementation(() => ({
    initialize: jest.fn().mockResolvedValue(undefined),
    cleanup: jest.fn().mockResolvedValue(undefined),
    convertFile: jest.fn().mockImplementation(async (inputPath, options) => {
      // Create a mock output file
      const outputPath = inputPath.replace('.mmd', `.${options.format}`);
      const mockImageBuffer = Buffer.from('mock-image-data');
      await fs.writeFile(outputPath, mockImageBuffer);
      
      return {
        success: true,
        inputFile: inputPath,
        outputFile: outputPath,
        format: options.format,
        size: mockImageBuffer.length,
        width: options.width || 800,
        height: options.height || 600,
        duration: 1000,
      };
    }),
  })),
}));

describe('Convert API Endpoints', () => {
  let server: ArchitekAIServer;
  let app: any;
  let tempDir: string;

  const sampleMermaidContent = `graph TD
    A[API Gateway] --> B[User Service]
    A --> C[Payment Service]
    B --> D[(User DB)]
    C --> E[(Payment DB)]`;

  beforeAll(async () => {
    // Create temporary directory for test files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'architek-ai-api-test-'));
    
    // Initialize server
    server = new ArchitekAIServer();
    await server.initialize();
    app = server.getApp();
  });

  afterAll(async () => {
    // Clean up
    await fs.remove(tempDir);
    if (server) {
      await server.stop();
    }
  });

  describe('POST /api/convert', () => {
    it('should convert Mermaid content to JPG', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: sampleMermaidContent,
          format: 'jpg',
          options: {
            quality: 90,
            width: 800,
            height: 600,
          },
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('image/jpeg');
      expect(response.headers['content-disposition']).toContain('diagram.jpg');
      expect(response.body).toBeInstanceOf(Buffer);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should convert Mermaid content to PNG', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: sampleMermaidContent,
          format: 'png',
          options: {
            width: 1024,
            height: 768,
          },
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('image/png');
      expect(response.headers['content-disposition']).toContain('diagram.png');
    });

    it('should convert Mermaid content to SVG', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: sampleMermaidContent,
          format: 'svg',
          options: {
            theme: 'dark',
          },
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('image/svg+xml');
      expect(response.headers['content-disposition']).toContain('diagram.svg');
    });

    it('should return 400 for missing content', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          format: 'jpg',
        })
        .expect(400);

      expect(response.body.error).toContain('content is required');
      expect(response.body.code).toBe('INVALID_CONTENT');
    });

    it('should return 400 for empty content', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: '',
          format: 'jpg',
        })
        .expect(400);

      expect(response.body.error).toContain('content is required');
      expect(response.body.code).toBe('INVALID_CONTENT');
    });

    it('should return 400 for invalid format', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: sampleMermaidContent,
          format: 'invalid',
        })
        .expect(400);

      expect(response.body.error).toContain('Invalid format');
      expect(response.body.code).toBe('INVALID_FORMAT');
    });

    it('should handle conversion options', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: sampleMermaidContent,
          format: 'jpg',
          options: {
            quality: 95,
            width: 1920,
            height: 1080,
            scale: 2,
            backgroundColor: '#f0f0f0',
            theme: 'forest',
          },
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('image/jpeg');
    });
  });

  describe('POST /api/convert/upload', () => {
    let testMermaidFile: string;

    beforeAll(async () => {
      testMermaidFile = path.join(tempDir, 'test-upload.mmd');
      await fs.writeFile(testMermaidFile, sampleMermaidContent);
    });

    it('should convert uploaded .mmd file to JPG', async () => {
      const response = await request(app)
        .post('/api/convert/upload')
        .attach('file', testMermaidFile)
        .field('format', 'jpg')
        .field('quality', '90')
        .field('width', '800')
        .field('height', '600')
        .expect(200);

      expect(response.headers['content-type']).toBe('image/jpeg');
      expect(response.headers['content-disposition']).toContain('test-upload.jpg');
    });

    it('should convert uploaded .mmd file to PNG', async () => {
      const response = await request(app)
        .post('/api/convert/upload')
        .attach('file', testMermaidFile)
        .field('format', 'png')
        .expect(200);

      expect(response.headers['content-type']).toBe('image/png');
    });

    it('should return 400 for missing file', async () => {
      const response = await request(app)
        .post('/api/convert/upload')
        .field('format', 'jpg')
        .expect(400);

      expect(response.body.error).toContain('No file uploaded');
      expect(response.body.code).toBe('NO_FILE');
    });

    it('should return 400 for invalid format in upload', async () => {
      const response = await request(app)
        .post('/api/convert/upload')
        .attach('file', testMermaidFile)
        .field('format', 'invalid')
        .expect(400);

      expect(response.body.error).toContain('Invalid format');
      expect(response.body.code).toBe('INVALID_FORMAT');
    });
  });

  describe('POST /api/convert/preview', () => {
    it('should generate SVG preview', async () => {
      const response = await request(app)
        .post('/api/convert/preview')
        .send({
          content: sampleMermaidContent,
          theme: 'default',
        })
        .expect(200);

      expect(response.body.svg).toBeDefined();
      expect(response.body.width).toBeGreaterThan(0);
      expect(response.body.height).toBeGreaterThan(0);
      expect(typeof response.body.svg).toBe('string');
    });

    it('should generate preview with different themes', async () => {
      const themes = ['default', 'dark', 'forest', 'neutral'];

      for (const theme of themes) {
        const response = await request(app)
          .post('/api/convert/preview')
          .send({
            content: sampleMermaidContent,
            theme,
          })
          .expect(200);

        expect(response.body.svg).toBeDefined();
        expect(response.body.width).toBeGreaterThan(0);
        expect(response.body.height).toBeGreaterThan(0);
      }
    });

    it('should return 400 for missing content in preview', async () => {
      const response = await request(app)
        .post('/api/convert/preview')
        .send({
          theme: 'default',
        })
        .expect(400);

      expect(response.body.error).toContain('content is required');
      expect(response.body.code).toBe('INVALID_CONTENT');
    });

    it('should return 400 for empty content in preview', async () => {
      const response = await request(app)
        .post('/api/convert/preview')
        .send({
          content: '',
          theme: 'default',
        })
        .expect(400);

      expect(response.body.error).toContain('content is required');
      expect(response.body.code).toBe('INVALID_CONTENT');
    });
  });

  describe('error handling', () => {
    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send('invalid json')
        .set('Content-Type', 'application/json')
        .expect(400);

      // Express should handle malformed JSON automatically
    });

    it('should handle large content', async () => {
      const largeContent = 'graph TD\n' + 'A --> B\n'.repeat(1000);
      
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: largeContent,
          format: 'svg',
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('image/svg+xml');
    });
  });

  describe('content-type handling', () => {
    it('should require application/json content-type', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send('content=test&format=jpg')
        .set('Content-Type', 'application/x-www-form-urlencoded')
        .expect(400);

      // Should fail because we expect JSON
    });
  });

  describe('response headers', () => {
    it('should set correct headers for JPG', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: sampleMermaidContent,
          format: 'jpg',
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('image/jpeg');
      expect(response.headers['content-disposition']).toMatch(/attachment; filename="diagram\.jpg"/);
      expect(response.headers['content-length']).toBeDefined();
    });

    it('should set correct headers for PNG', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: sampleMermaidContent,
          format: 'png',
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('image/png');
      expect(response.headers['content-disposition']).toMatch(/attachment; filename="diagram\.png"/);
    });

    it('should set correct headers for SVG', async () => {
      const response = await request(app)
        .post('/api/convert')
        .send({
          content: sampleMermaidContent,
          format: 'svg',
        })
        .expect(200);

      expect(response.headers['content-type']).toBe('image/svg+xml');
      expect(response.headers['content-disposition']).toMatch(/attachment; filename="diagram\.svg"/);
    });
  });
});
