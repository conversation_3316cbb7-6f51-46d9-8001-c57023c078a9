# Serverless Architecture Template
# Event-driven serverless architecture with functions, queues, and managed services

id: "serverless"
name: "Serverless Architecture"
description: "Event-driven serverless architecture with functions, queues, and managed services"
pattern: "serverless"
version: "1.0.0"
author: "ArchitekAI"

metadata:
  tags:
    - "serverless"
    - "event-driven"
    - "cloud-native"
    - "auto-scaling"
  complexity: "moderate"
  useCases:
    - "event processing"
    - "API backends"
    - "data processing pipelines"
    - "IoT applications"
  prerequisites:
    - "cloud platform knowledge"
    - "function-as-a-service experience"
    - "event-driven architecture"
  documentation: |
    This template provides a foundation for serverless architecture with:
    - Function-as-a-Service (FaaS) components
    - Event-driven communication
    - Managed cloud services
    - Auto-scaling capabilities
    - Pay-per-use pricing model

components:
  - id: "external"
    name: "External Clients"
    type: "external"
    description: "External clients, users, and applications"
    properties:
      type: "client"
    position:
      x: 200
      y: -50

  - id: "api-gateway"
    name: "API Gateway"
    type: "api_gateway"
    description: "Managed API gateway for HTTP requests"
    properties:
      provider: "AWS API Gateway"
      features: ["authentication", "throttling", "caching"]
    position:
      x: 200
      y: 50

  - id: "auth-function"
    name: "Authentication Function"
    type: "function"
    description: "Handles user authentication and authorization"
    properties:
      runtime: "nodejs18"
      memory: 256
      timeout: 30
      triggers: ["api-gateway"]
    position:
      x: 100
      y: 150

  - id: "user-function"
    name: "User Management Function"
    type: "function"
    description: "Manages user CRUD operations"
    properties:
      runtime: "nodejs18"
      memory: 512
      timeout: 60
      triggers: ["api-gateway"]
    position:
      x: 300
      y: 150

  - id: "order-function"
    name: "Order Processing Function"
    type: "function"
    description: "Processes order creation and updates"
    properties:
      runtime: "python3.9"
      memory: 1024
      timeout: 300
      triggers: ["api-gateway", "event-queue"]
    position:
      x: 500
      y: 150

  - id: "notification-function"
    name: "Notification Function"
    type: "function"
    description: "Sends notifications via multiple channels"
    properties:
      runtime: "nodejs18"
      memory: 256
      timeout: 30
      triggers: ["event-queue"]
    position:
      x: 700
      y: 150

  - id: "event-queue"
    name: "Event Queue"
    type: "queue"
    description: "Managed message queue for async processing"
    properties:
      type: "AWS SQS"
      visibilityTimeout: 300
      messageRetention: 1209600
    position:
      x: 400
      y: 250

  - id: "user-database"
    name: "User Database"
    type: "database"
    description: "Managed NoSQL database for user data"
    properties:
      type: "DynamoDB"
      billingMode: "on-demand"
      encryption: true
    position:
      x: 300
      y: 350

  - id: "file-storage"
    name: "File Storage"
    type: "storage"
    description: "Object storage for files and assets"
    properties:
      type: "AWS S3"
      encryption: true
      versioning: true
    position:
      x: 500
      y: 350

  - id: "cdn"
    name: "Content Delivery Network"
    type: "cdn"
    description: "Global content distribution"
    properties:
      provider: "CloudFront"
      caching: true
      compression: true
    position:
      x: 200
      y: 350

connections:
  - id: "client-to-cdn"
    source: "external"
    target: "cdn"
    type: "https"
    label: "Static Content"

  - id: "client-to-api"
    source: "external"
    target: "api-gateway"
    type: "https"
    label: "API Requests"

  - id: "api-to-auth"
    source: "api-gateway"
    target: "auth-function"
    type: "api_call"
    label: "Auth Requests"

  - id: "api-to-user"
    source: "api-gateway"
    target: "user-function"
    type: "api_call"
    label: "User Requests"

  - id: "api-to-order"
    source: "api-gateway"
    target: "order-function"
    type: "api_call"
    label: "Order Requests"

  - id: "order-to-queue"
    source: "order-function"
    target: "event-queue"
    type: "message_queue"
    label: "Order Events"

  - id: "queue-to-notification"
    source: "event-queue"
    target: "notification-function"
    type: "message_queue"
    label: "Notification Events"

  - id: "user-to-db"
    source: "user-function"
    target: "user-database"
    type: "database_connection"
    label: "User Data"

  - id: "functions-to-storage"
    source: "order-function"
    target: "file-storage"
    type: "api_call"
    label: "File Operations"

  - id: "cdn-to-storage"
    source: "cdn"
    target: "file-storage"
    type: "api_call"
    label: "Origin Requests"

customization:
  allowComponentAddition: true
  allowComponentRemoval: true
  allowConnectionModification: true
  requiredComponents:
    - "api-gateway"
  optionalComponents:
    - "cdn"
    - "event-queue"
  configurationSchema:
    runtime:
      type: "string"
      enum: ["nodejs18", "python3.9", "java11", "dotnet6"]
      default: "nodejs18"
    databaseType:
      type: "string"
      enum: ["DynamoDB", "RDS", "DocumentDB"]
      default: "DynamoDB"
    eventDriven:
      type: "boolean"
      default: true
    cdn:
      type: "boolean"
      default: true
