/**
 * Template API Routes for ArchitekAI
 * 
 * Handles template management via REST API.
 */

import { Router, Request, Response, NextFunction } from 'express';
import { TemplateManager } from '@/core/templates/manager';
import { ArchitecturePattern } from '@/types/architecture';
import { createLogger } from '@/utils/logger';

const logger = createLogger('TemplateRoutes');

export function templateRoutes(templateManager: TemplateManager): Router {
  const router = Router();

  /**
   * GET /api/templates
   * List all available templates
   */
  router.get('/', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { pattern, complexity, tags, search } = req.query;
      
      let templates = templateManager.getTemplates();

      // Apply filters
      if (pattern && typeof pattern === 'string') {
        templates = templateManager.getTemplatesByPattern(pattern as ArchitecturePattern);
      }

      if (complexity && typeof complexity === 'string') {
        templates = templateManager.getTemplatesByComplexity(complexity as any);
      }

      if (tags && typeof tags === 'string') {
        const tagArray = tags.split(',').map(tag => tag.trim());
        templates = templateManager.searchTemplatesByTags(tagArray);
      }

      if (search && typeof search === 'string') {
        const searchTerm = search.toLowerCase();
        templates = templates.filter(template => 
          template.name.toLowerCase().includes(searchTerm) ||
          template.description.toLowerCase().includes(searchTerm) ||
          template.metadata.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      }

      // Sort by name
      templates.sort((a, b) => a.name.localeCompare(b.name));

      res.json({
        success: true,
        data: {
          templates: templates.map(template => ({
            id: template.id,
            name: template.name,
            description: template.description,
            pattern: template.pattern,
            complexity: template.metadata.complexity,
            tags: template.metadata.tags,
            componentCount: template.components.length,
            connectionCount: template.connections.length,
            useCases: template.metadata.useCases,
            author: template.metadata.author,
            version: template.metadata.version,
          })),
          total: templates.length,
          filters: {
            pattern: pattern || null,
            complexity: complexity || null,
            tags: tags || null,
            search: search || null,
          },
        },
      });

    } catch (error) {
      logger.error('Failed to list templates:', error);
      next(error);
    }
  });

  /**
   * GET /api/templates/:id
   * Get a specific template by ID
   */
  router.get('/:id', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Template ID is required'
        });
      }
      const template = await templateManager.getTemplate(id);

      res.json({
        success: true,
        data: {
          template,
        },
      });

    } catch (error) {
      if (error instanceof Error && error.message.includes('not found')) {
        return res.status(404).json({
          error: `Template not found: ${req.params.id}`,
          code: 'TEMPLATE_NOT_FOUND',
        });
      }
      
      logger.error('Failed to get template:', error);
      return next(error);
    }
  });

  /**
   * POST /api/templates
   * Create a new template
   */
  router.post('/', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const template = req.body;

      // Basic validation
      if (!template.id || !template.name || !template.description) {
        return res.status(400).json({
          error: 'Template must have id, name, and description',
          code: 'INVALID_TEMPLATE',
        });
      }

      // Check if template already exists
      try {
        await templateManager.getTemplate(template.id);
        return res.status(409).json({
          error: `Template with ID '${template.id}' already exists`,
          code: 'TEMPLATE_EXISTS',
        });
      } catch {
        // Template doesn't exist, which is what we want
      }

      // Validate template
      const validation = await templateManager.validateTemplate(template);
      if (!validation.isValid) {
        return res.status(400).json({
          error: 'Template validation failed',
          code: 'TEMPLATE_VALIDATION_FAILED',
          details: validation.errors,
        });
      }

      // Save template
      await templateManager.saveTemplate(template);

      res.status(201).json({
        success: true,
        data: {
          template: {
            id: template.id,
            name: template.name,
            description: template.description,
            pattern: template.pattern,
          },
          message: 'Template created successfully',
        },
      });

      logger.info(`Template created: ${template.id}`);

    } catch (error) {
      logger.error('Failed to create template:', error);
      return next(error);
    }
  });

  /**
   * PUT /api/templates/:id
   * Update an existing template
   */
  router.put('/:id', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Template ID is required'
        });
      }
      const updatedTemplate = req.body;

      // Ensure ID matches
      if (updatedTemplate.id && updatedTemplate.id !== id) {
        return res.status(400).json({
          error: 'Template ID in body must match URL parameter',
          code: 'ID_MISMATCH',
        });
      }

      updatedTemplate.id = id;

      // Check if template exists
      try {
        await templateManager.getTemplate(id);
      } catch {
        return res.status(404).json({
          error: `Template not found: ${id}`,
          code: 'TEMPLATE_NOT_FOUND',
        });
      }

      // Validate template
      const validation = await templateManager.validateTemplate(updatedTemplate);
      if (!validation.isValid) {
        return res.status(400).json({
          error: 'Template validation failed',
          code: 'TEMPLATE_VALIDATION_FAILED',
          details: validation.errors,
        });
      }

      // Save updated template
      await templateManager.saveTemplate(updatedTemplate);

      res.json({
        success: true,
        data: {
          template: {
            id: updatedTemplate.id,
            name: updatedTemplate.name,
            description: updatedTemplate.description,
            pattern: updatedTemplate.pattern,
          },
          message: 'Template updated successfully',
        },
      });

      logger.info(`Template updated: ${id}`);

    } catch (error) {
      logger.error('Failed to update template:', error);
      return next(error);
    }
  });

  /**
   * DELETE /api/templates/:id
   * Delete a template
   */
  router.delete('/:id', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Template ID is required'
        });
      }

      // Check if template exists
      try {
        await templateManager.getTemplate(id);
      } catch {
        return res.status(404).json({
          error: `Template not found: ${id}`,
          code: 'TEMPLATE_NOT_FOUND',
        });
      }

      // Delete template
      await templateManager.deleteTemplate(id);

      res.json({
        success: true,
        data: {
          message: 'Template deleted successfully',
        },
      });

      logger.info(`Template deleted: ${id}`);

    } catch (error) {
      logger.error('Failed to delete template:', error);
      return next(error);
    }
  });

  /**
   * POST /api/templates/:id/apply
   * Apply a template to generate an architecture
   */
  router.post('/:id/apply', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Template ID is required'
        });
      }
      const { customizations = {}, baseArchitecture } = req.body;

      // Get template
      let template;
      try {
        template = await templateManager.getTemplate(id);
      } catch {
        return res.status(404).json({
          error: `Template not found: ${id}`,
          code: 'TEMPLATE_NOT_FOUND',
        });
      }

      // Apply customizations if provided
      if (Object.keys(customizations).length > 0) {
        template = await templateManager.customizeTemplate(template, customizations);
      }

      // Apply template
      const architecture = await templateManager.applyTemplate(template, baseArchitecture);

      res.json({
        success: true,
        data: {
          architecture,
          template: {
            id: template.id,
            name: template.name,
            pattern: template.pattern,
          },
          customizations: Object.keys(customizations).length > 0 ? customizations : null,
        },
      });

      logger.info(`Template applied: ${id}`);

    } catch (error) {
      logger.error('Failed to apply template:', error);
      return next(error);
    }
  });

  /**
   * POST /api/templates/:id/validate
   * Validate a template
   */
  router.post('/:id/validate', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({
          success: false,
          error: 'Template ID is required'
        });
      }

      // Get template
      let template;
      try {
        template = await templateManager.getTemplate(id);
      } catch {
        return res.status(404).json({
          error: `Template not found: ${id}`,
          code: 'TEMPLATE_NOT_FOUND',
        });
      }

      // Validate template
      const validation = await templateManager.validateTemplate(template);

      res.json({
        success: true,
        data: {
          validation,
          template: {
            id: template.id,
            name: template.name,
          },
        },
      });

    } catch (error) {
      logger.error('Failed to validate template:', error);
      return next(error);
    }
  });

  /**
   * GET /api/templates/patterns
   * Get available architecture patterns
   */
  router.get('/patterns', (_req: Request, res: Response) => {
    try {
      const patterns = Object.values(ArchitecturePattern).map(pattern => ({
        id: pattern,
        name: pattern.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        description: getPatternDescription(pattern),
      }));

      res.json({
        success: true,
        data: {
          patterns,
        },
      });

    } catch (error) {
      logger.error('Failed to get patterns:', error);
      res.status(500).json({
        error: 'Failed to retrieve patterns',
        code: 'PATTERNS_ERROR',
      });
    }
  });

  return router;
}

/**
 * Get description for architecture pattern
 */
function getPatternDescription(pattern: ArchitecturePattern): string {
  const descriptions: Record<ArchitecturePattern, string> = {
    [ArchitecturePattern.MICROSERVICES]: 'Distributed architecture with loosely coupled services',
    [ArchitecturePattern.MONOLITHIC]: 'Single deployable unit with all functionality',
    [ArchitecturePattern.SERVERLESS]: 'Event-driven architecture using functions as a service',
    [ArchitecturePattern.EVENT_DRIVEN]: 'Architecture based on event production and consumption',
    [ArchitecturePattern.LAYERED]: 'Organized into horizontal layers with specific responsibilities',
    [ArchitecturePattern.HEXAGONAL]: 'Ports and adapters architecture for testability',
    [ArchitecturePattern.CLEAN_ARCHITECTURE]: 'Dependency inversion with clean boundaries',
    [ArchitecturePattern.MVC]: 'Model-View-Controller separation of concerns',
    [ArchitecturePattern.MVP]: 'Model-View-Presenter pattern for UI applications',
    [ArchitecturePattern.MVVM]: 'Model-View-ViewModel pattern with data binding',
    [ArchitecturePattern.PIPE_AND_FILTER]: 'Data processing through a series of filters',
    [ArchitecturePattern.BLACKBOARD]: 'Shared knowledge base with multiple specialists',
    [ArchitecturePattern.BROKER]: 'Intermediary for distributed component communication',
    [ArchitecturePattern.PEER_TO_PEER]: 'Decentralized network of equal participants',
    [ArchitecturePattern.SERVICE_ORIENTED]: 'Services communicating through well-defined interfaces',
    [ArchitecturePattern.SPACE_BASED]: 'Distributed processing with shared memory spaces',
    [ArchitecturePattern.MASTER_SLAVE]: 'Master component controlling multiple slave components',
    [ArchitecturePattern.CUSTOM]: 'Custom architecture pattern not fitting standard categories',
  };

  return descriptions[pattern] || 'No description available';
}
