name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Use Node.js 18.x
      uses: actions/setup-node@v4
      with:
        node-version: 18.x
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm run test:coverage
      
    - name: Build project
      run: npm run build

  release:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Use Node.js 18.x
      uses: actions/setup-node@v4
      with:
        node-version: 18.x
        cache: 'npm'
        registry-url: 'https://registry.npmjs.org'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build project
      run: npm run build
      
    - name: Create release package
      run: npm pack
      
    - name: Get package info
      id: package
      run: |
        echo "name=$(node -p "require('./package.json').name")" >> $GITHUB_OUTPUT
        echo "version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT
        
    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        body: |
          ## Changes in this Release
          
          ### Features
          - Intelligent architecture diagram generation from natural language
          - Support for multiple output formats (Mermaid, PlantUML, ASCII, Draw.io, Lucidchart)
          - Pre-built templates for common architecture patterns
          - Interactive CLI with customization options
          - Batch processing capabilities
          - Web API and frontend interface
          
          ### Installation
          ```bash
          npm install -g ${{ steps.package.outputs.name }}@${{ steps.package.outputs.version }}
          ```
          
          ### Usage
          ```bash
          architek-ai generate "microservices with API gateway and user service"
          architek-ai template use microservices --customize
          architek-ai batch --input "./descriptions/*.txt"
          ```
          
          For more information, see the [README](https://github.com/HectorTa1989/ArchitekAI#readme).
        draft: false
        prerelease: false
        
    - name: Publish to NPM
      run: npm publish
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  docker:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
        
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: hectortai1989/architek-ai
        tags: |
          type=ref,event=tag
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
          
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        platforms: linux/amd64,linux/arm64
        cache-from: type=gha
        cache-to: type=gha,mode=max

  homebrew:
    runs-on: ubuntu-latest
    needs: release
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Update Homebrew formula
      uses: dawidd6/action-homebrew-bump-formula@v3
      with:
        token: ${{ secrets.HOMEBREW_TOKEN }}
        formula: architek-ai
        tag: ${{ github.ref }}
        revision: ${{ github.sha }}
        force: false
