graph TD
    web_client[Web Client]
    load_balancer[/Load Balancer\]
    application_server[Application Server]
    database[(Database)]
    cache[[Cache]]
    comp_1[(Database)]
    comp_2[/Load Balancer\]
    comp_3[/Balancer\]
    comp_4[[Cache]]
    comp_5[Web App]

    web_client ==>|User Requests| load_balancer
    load_balancer -->|Application Requests| application_server
    application_server -.->|Data Operations| database
    application_server -->|Cache Operations| cache
