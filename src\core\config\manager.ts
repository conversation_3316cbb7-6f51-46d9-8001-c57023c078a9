/**
 * Configuration Manager for ArchitekAI
 * 
 * Handles loading, merging, and managing configuration from multiple sources.
 */

import fs from 'fs-extra';
import path from 'path';
import yaml from 'yaml';
import Joi from 'joi';
import { merge } from 'lodash';
import {
  AppConfig,
  UserConfig,
  ProjectConfig,
  ConfigValidationResult,
  Environment,
  LogLevel,
  LogFormat,
  LogOutput,
  OutputFormat,
  NLPEngine
} from '@/types/config';
import { createLogger } from '@/utils/logger';

const logger = createLogger('ConfigManager');

export class ConfigManager {
  private config: AppConfig;
  private globalConfigPath: string;
  private localConfigPath: string;
  private userConfigPath: string;
  private schema: Joi.ObjectSchema;

  constructor() {
    this.config = this.getDefaultConfig();
    this.globalConfigPath = this.getGlobalConfigPath();
    this.localConfigPath = this.getLocalConfigPath();
    this.userConfigPath = this.getUserConfigPath();
    this.schema = this.createConfigSchema();
  }

  /**
   * Initialize the configuration manager
   */
  async initialize(): Promise<void> {
    try {
      // Load configurations in order of precedence
      const defaultConfig = this.getDefaultConfig();
      const globalConfig = await this.loadGlobalConfig();
      const userConfig = await this.loadUserConfig();
      const localConfig = await this.loadLocalConfig();
      const envConfig = this.loadEnvironmentConfig();

      // Merge configurations (later configs override earlier ones)
      this.config = merge(
        {},
        defaultConfig,
        globalConfig,
        userConfig,
        localConfig,
        envConfig
      );

      // Validate merged configuration
      const validation = await this.validate();
      if (!validation.isValid) {
        logger.warn('Configuration validation failed:', validation.errors);
        // Continue with invalid config but log warnings
      }

      logger.debug('Configuration initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize configuration:', error);
      throw new Error(`Configuration initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get the current configuration
   */
  getConfig(): AppConfig {
    return this.config;
  }

  /**
   * Set a configuration value
   */
  async set(key: string, value: unknown): Promise<void> {
    const keys = key.split('.');
    let current: any = this.config;

    // Navigate to the parent object
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i]!;
      if (!(k in current)) {
        current[k] = {};
      }
      current = current[k];
    }

    // Set the value
    const lastKey = keys[keys.length - 1]!;
    current[lastKey] = value;

    logger.debug(`Configuration updated: ${key} = ${JSON.stringify(value)}`);
  }

  /**
   * Save configuration to local config file
   */
  async save(): Promise<void> {
    try {
      await fs.ensureDir(path.dirname(this.localConfigPath));
      
      const configToSave = {
        // Only save non-default values
        ...this.config,
        // Remove sensitive or runtime-only data
        version: undefined,
        environment: undefined,
      };

      const yamlContent = yaml.stringify(configToSave, {
        indent: 2,
        lineWidth: 120,
      });

      await fs.writeFile(this.localConfigPath, yamlContent, 'utf8');
      logger.debug(`Configuration saved to: ${this.localConfigPath}`);
    } catch (error) {
      logger.error('Failed to save configuration:', error);
      throw new Error(`Failed to save configuration: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Reset configuration to defaults
   */
  async reset(scope: 'global' | 'local' | 'user' | 'all' = 'local'): Promise<void> {
    try {
      if (scope === 'local' || scope === 'all') {
        if (await fs.pathExists(this.localConfigPath)) {
          await fs.remove(this.localConfigPath);
          logger.debug('Local configuration reset');
        }
      }

      if (scope === 'user' || scope === 'all') {
        if (await fs.pathExists(this.userConfigPath)) {
          await fs.remove(this.userConfigPath);
          logger.debug('User configuration reset');
        }
      }

      if (scope === 'global' || scope === 'all') {
        if (await fs.pathExists(this.globalConfigPath)) {
          await fs.remove(this.globalConfigPath);
          logger.debug('Global configuration reset');
        }
      }

      // Reinitialize configuration
      await this.initialize();
    } catch (error) {
      logger.error('Failed to reset configuration:', error);
      throw new Error(`Failed to reset configuration: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Validate current configuration
   */
  async validate(): Promise<ConfigValidationResult> {
    try {
      const { error } = this.schema.validate(this.config, { 
        abortEarly: false,
        allowUnknown: true,
      });

      if (error) {
        const errors = error.details.map(detail => ({
          path: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value,
          expected: detail.type,
        }));

        return {
          isValid: false,
          errors,
          warnings: [],
        };
      }

      return {
        isValid: true,
        errors: [],
        warnings: [],
      };
    } catch (error) {
      logger.error('Configuration validation error:', error);
      return {
        isValid: false,
        errors: [{
          path: 'root',
          message: error instanceof Error ? error.message : 'Unknown validation error',
          value: this.config,
          expected: 'valid configuration',
        }],
        warnings: [],
      };
    }
  }

  /**
   * Load configuration from file
   */
  async loadConfig(filePath: string): Promise<void> {
    try {
      if (await fs.pathExists(filePath)) {
        const content = await fs.readFile(filePath, 'utf8');
        const fileConfig = this.parseConfigFile(content, filePath);
        
        // Merge with current config
        this.config = merge({}, this.config, fileConfig);
        
        logger.debug(`Configuration loaded from: ${filePath}`);
      }
    } catch (error) {
      logger.error(`Failed to load configuration from ${filePath}:`, error);
      throw new Error(`Failed to load configuration: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get configuration file paths
   */
  getGlobalConfigPath(): string {
    return path.join(this.getConfigDirectory(), 'config.yaml');
  }

  getLocalConfigPath(): string {
    return path.join(process.cwd(), '.architekAI.yaml');
  }

  getUserConfigPath(): string {
    return path.join(this.getHomeDirectory(), '.architekAI', 'user.yaml');
  }

  getActiveConfigPath(): string {
    return this.localConfigPath;
  }

  /**
   * Private methods
   */
  private getDefaultConfig(): AppConfig {
    return {
      version: '1.0.0',
      environment: Environment.DEVELOPMENT,
      logging: {
        level: LogLevel.INFO,
        format: LogFormat.SIMPLE,
        outputs: [LogOutput.CONSOLE, LogOutput.FILE],
        file: {
          path: './logs/architekAI.log',
          maxSize: '10MB',
          maxFiles: 5,
          compress: true,
        },
        console: {
          colors: true,
          timestamp: true,
          level: true,
        },
      },
      output: {
        defaultFormat: OutputFormat.MERMAID,
        directory: './diagrams',
        filename: 'architecture',
        overwrite: false,
        backup: true,
        compression: false,
        formats: [
          { format: OutputFormat.MERMAID, enabled: true, options: {} },
          { format: OutputFormat.PLANTUML, enabled: true, options: {} },
          { format: OutputFormat.ASCII, enabled: true, options: {} },
          { format: OutputFormat.DRAWIO, enabled: true, options: {} },
          { format: OutputFormat.LUCIDCHART, enabled: true, options: {} },
        ],
      },
      nlp: {
        engine: NLPEngine.NATURAL,
        confidence: {
          minimum: 0.6,
          component: 0.7,
          connection: 0.6,
          pattern: 0.8,
        },
        preprocessing: {
          normalize: true,
          removeStopWords: true,
          stemming: false,
          lemmatization: true,
          customFilters: [],
        },
        postprocessing: {
          validation: true,
          optimization: true,
          beautification: true,
          customProcessors: [],
        },
        customPatterns: [],
      },
      templates: {
        directory: './templates',
        autoLoad: true,
        validation: true,
        customTemplates: [],
      },
      web: {
        enabled: false,
        port: 3000,
        host: 'localhost',
        cors: {
          enabled: true,
          origin: '*',
          methods: ['GET', 'POST', 'PUT', 'DELETE'],
          allowedHeaders: ['Content-Type', 'Authorization'],
          credentials: false,
        },
        security: {
          helmet: true,
          rateLimit: true,
          authentication: {
            enabled: false,
            type: 'jwt',
          },
          authorization: {
            enabled: false,
            roles: [],
            permissions: [],
          },
        },
        rateLimit: {
          enabled: true,
          windowMs: 15 * 60 * 1000, // 15 minutes
          max: 100,
          message: 'Too many requests',
          standardHeaders: true,
          legacyHeaders: false,
        },
        static: {
          enabled: true,
          directory: './public',
          maxAge: 86400000, // 1 day
          etag: true,
          index: ['index.html'],
        },
      },
      cli: {
        interactive: true,
        colors: true,
        progress: true,
        verbose: false,
        confirmations: true,
        shortcuts: {},
      },
      plugins: {
        enabled: true,
        directory: './plugins',
        autoLoad: true,
        whitelist: [],
        blacklist: [],
      },
    };
  }

  private async loadGlobalConfig(): Promise<Partial<AppConfig>> {
    return this.loadConfigFile(this.globalConfigPath);
  }

  private async loadUserConfig(): Promise<Partial<UserConfig>> {
    return this.loadConfigFile(this.userConfigPath);
  }

  private async loadLocalConfig(): Promise<Partial<ProjectConfig>> {
    return this.loadConfigFile(this.localConfigPath);
  }

  private loadEnvironmentConfig(): Partial<AppConfig> {
    const envConfig: Partial<AppConfig> = {};

    // Map environment variables to config
    if (process.env.ARCHITEKTI_LOG_LEVEL) {
      envConfig.logging = {
        level: process.env.ARCHITEKTI_LOG_LEVEL as LogLevel,
        format: LogFormat.SIMPLE,
        outputs: [LogOutput.CONSOLE]
      };
    }

    if (process.env.ARCHITEKTI_OUTPUT_FORMAT) {
      envConfig.output = {
        defaultFormat: process.env.ARCHITEKTI_OUTPUT_FORMAT as OutputFormat,
        directory: './diagrams',
        filename: 'architecture',
        overwrite: false,
        backup: true,
        compression: false,
        formats: []
      };
    }

    if (process.env.ARCHITEKTI_WEB_PORT) {
      const port = parseInt(process.env.ARCHITEKTI_WEB_PORT);
      if (!isNaN(port)) {
        envConfig.web = {
          enabled: true,
          port,
          host: 'localhost',
          cors: { enabled: false, origin: '*', methods: [], allowedHeaders: [], credentials: false },
          security: {
            helmet: true,
            rateLimit: true,
            authentication: { enabled: false, type: 'jwt' },
            authorization: { enabled: false, roles: [], permissions: [] }
          },
          rateLimit: { enabled: true, windowMs: 15 * 60 * 1000, max: 100, message: 'Too many requests', standardHeaders: true, legacyHeaders: false },
          static: { enabled: true, directory: './public', maxAge: 3600000, etag: true, index: ['index.html'] }
        };
      }
    }

    return envConfig;
  }

  private async loadConfigFile(filePath: string): Promise<any> {
    try {
      if (await fs.pathExists(filePath)) {
        const content = await fs.readFile(filePath, 'utf8');
        return this.parseConfigFile(content, filePath);
      }
      return {};
    } catch (error) {
      logger.warn(`Failed to load config file ${filePath}:`, error);
      return {};
    }
  }

  private parseConfigFile(content: string, filePath: string): any {
    const ext = path.extname(filePath).toLowerCase();
    
    try {
      switch (ext) {
        case '.yaml':
        case '.yml':
          return yaml.parse(content);
        case '.json':
          return JSON.parse(content);
        default:
          throw new Error(`Unsupported config file format: ${ext}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse config file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private createConfigSchema(): Joi.ObjectSchema {
    return Joi.object({
      version: Joi.string().required(),
      environment: Joi.string().valid('development', 'testing', 'staging', 'production').required(),
      logging: Joi.object({
        level: Joi.string().valid('error', 'warn', 'info', 'http', 'verbose', 'debug', 'silly').required(),
        format: Joi.string().valid('json', 'simple', 'combined', 'common', 'dev', 'short', 'tiny').required(),
        outputs: Joi.array().items(Joi.string().valid('console', 'file', 'http', 'stream')).required(),
      }).required(),
      output: Joi.object({
        defaultFormat: Joi.string().valid('mermaid', 'plantuml', 'ascii', 'drawio', 'lucidchart', 'json', 'yaml', 'svg', 'png', 'pdf').required(),
        directory: Joi.string().required(),
        filename: Joi.string().required(),
        overwrite: Joi.boolean().required(),
        backup: Joi.boolean().required(),
        compression: Joi.boolean().required(),
      }).required(),
      // Add more schema validation as needed
    }).unknown(true); // Allow unknown properties for extensibility
  }

  private getHomeDirectory(): string {
    return process.env.HOME || process.env.USERPROFILE || process.cwd();
  }

  private getConfigDirectory(): string {
    const homeDir = this.getHomeDirectory();
    return path.join(homeDir, '.architekAI');
  }
}
