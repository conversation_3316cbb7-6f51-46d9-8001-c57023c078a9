# ArchitekAI Default Configuration
# This file contains the default settings for ArchitekAI

version: "1.0.0"
environment: "development"

# Logging configuration
logging:
  level: "info"
  format: "simple"
  outputs:
    - "console"
    - "file"
  file:
    path: "./logs/architekAI.log"
    maxSize: "10MB"
    maxFiles: 5
    compress: true
  console:
    colors: true
    timestamp: true
    level: true

# Output configuration
output:
  defaultFormat: "mermaid"
  directory: "./diagrams"
  filename: "architecture"
  overwrite: false
  backup: true
  compression: false
  formats:
    - format: "mermaid"
      enabled: true
      options: {}
    - format: "plantuml"
      enabled: true
      options: {}
    - format: "ascii"
      enabled: true
      options: {}
    - format: "drawio"
      enabled: true
      options: {}
    - format: "lucidchart"
      enabled: true
      options: {}

# Natural Language Processing configuration
nlp:
  engine: "natural"
  confidence:
    minimum: 0.6
    component: 0.7
    connection: 0.6
    pattern: 0.8
  preprocessing:
    normalize: true
    removeStopWords: true
    stemming: false
    lemmatization: true
    customFilters: []
  postprocessing:
    validation: true
    optimization: true
    beautification: true
    customProcessors: []
  customPatterns: []

# Template configuration
templates:
  directory: "./templates"
  autoLoad: true
  validation: true
  customTemplates: []

# Web server configuration
web:
  enabled: false
  port: 3000
  host: "localhost"
  cors:
    enabled: true
    origin: "*"
    methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
    allowedHeaders:
      - "Content-Type"
      - "Authorization"
    credentials: false
  security:
    helmet: true
    rateLimit: true
    authentication:
      enabled: false
      type: "jwt"
    authorization:
      enabled: false
      roles: []
      permissions: []
  rateLimit:
    enabled: true
    windowMs: 900000  # 15 minutes
    max: 100
    message: "Too many requests"
    standardHeaders: true
    legacyHeaders: false
  static:
    enabled: true
    directory: "./public"
    maxAge: 86400000  # 1 day
    etag: true
    index:
      - "index.html"

# CLI configuration
cli:
  interactive: true
  colors: true
  progress: true
  verbose: false
  confirmations: true
  shortcuts: {}

# Plugin configuration
plugins:
  enabled: true
  directory: "./plugins"
  autoLoad: true
  whitelist: []
  blacklist: []
