/**
 * Mermaid Converter for ArchitekAI
 * 
 * <PERSON>les conversion of Mermaid (.mmd) files to various image formats
 * using Puppeteer for headless browser rendering.
 */

import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import sharp from 'sharp';
import fs from 'fs-extra';
import path from 'path';
import { createLogger } from '@/utils/logger';

const logger = createLogger('MermaidConverter');

export interface ConversionOptions {
  format: 'jpg' | 'jpeg' | 'png' | 'svg' | 'pdf';
  quality?: number; // 1-100 for JPEG
  width?: number;
  height?: number;
  scale?: number; // Device scale factor
  backgroundColor?: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  outputDir?: string;
  overwrite?: boolean;
}

export interface ConversionResult {
  inputFile: string;
  outputFile: string;
  format: string;
  size: number;
  width: number;
  height: number;
  success: boolean;
  error?: string;
  duration: number;
}

export interface BatchConversionResult {
  results: ConversionResult[];
  totalProcessed: number;
  successful: number;
  failed: number;
  totalDuration: number;
  errors: Array<{ file: string; error: string }>;
}

export class MermaidConverter {
  private browser: Browser | null = null;
  private isInitialized = false;

  constructor() {}

  /**
   * Initialize the converter with Puppeteer browser instance
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.info('Initializing Mermaid converter...');
      
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      this.isInitialized = true;
      logger.info('Mermaid converter initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Mermaid converter:', error);
      throw new Error(`Converter initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.isInitialized = false;
      logger.info('Mermaid converter cleaned up');
    }
  }

  /**
   * Convert a single Mermaid file to the specified format
   */
  async convertFile(inputPath: string, options: ConversionOptions): Promise<ConversionResult> {
    const startTime = Date.now();
    
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Validate input file
      if (!await fs.pathExists(inputPath)) {
        throw new Error(`Input file not found: ${inputPath}`);
      }

      const inputContent = await fs.readFile(inputPath, 'utf8');
      if (!inputContent.trim()) {
        throw new Error('Input file is empty');
      }

      // Generate output path
      const outputPath = this.generateOutputPath(inputPath, options);
      
      // Check if output file exists and handle overwrite
      if (!options.overwrite && await fs.pathExists(outputPath)) {
        throw new Error(`Output file already exists: ${outputPath}`);
      }

      // Ensure output directory exists
      await fs.ensureDir(path.dirname(outputPath));

      // Convert the diagram
      const { buffer, width, height } = await this.renderMermaidDiagram(inputContent, options);
      
      // Save the result
      await fs.writeFile(outputPath, buffer);
      const stats = await fs.stat(outputPath);

      const duration = Date.now() - startTime;

      return {
        inputFile: inputPath,
        outputFile: outputPath,
        format: options.format,
        size: stats.size,
        width,
        height,
        success: true,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      logger.error(`Conversion failed for ${inputPath}:`, error);
      
      return {
        inputFile: inputPath,
        outputFile: '',
        format: options.format,
        size: 0,
        width: 0,
        height: 0,
        success: false,
        error: errorMessage,
        duration
      };
    }
  }

  /**
   * Convert multiple Mermaid files in batch
   */
  async convertBatch(inputPaths: string[], options: ConversionOptions): Promise<BatchConversionResult> {
    const startTime = Date.now();
    const results: ConversionResult[] = [];
    const errors: Array<{ file: string; error: string }> = [];

    logger.info(`Starting batch conversion of ${inputPaths.length} files`);

    for (const inputPath of inputPaths) {
      try {
        const result = await this.convertFile(inputPath, options);
        results.push(result);
        
        if (!result.success && result.error) {
          errors.push({ file: inputPath, error: result.error });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        errors.push({ file: inputPath, error: errorMessage });
        
        // Add failed result
        results.push({
          inputFile: inputPath,
          outputFile: '',
          format: options.format,
          size: 0,
          width: 0,
          height: 0,
          success: false,
          error: errorMessage,
          duration: 0
        });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;
    const totalDuration = Date.now() - startTime;

    logger.info(`Batch conversion completed: ${successful} successful, ${failed} failed`);

    return {
      results,
      totalProcessed: results.length,
      successful,
      failed,
      totalDuration,
      errors
    };
  }

  /**
   * Render Mermaid diagram using Puppeteer
   */
  private async renderMermaidDiagram(
    mermaidContent: string, 
    options: ConversionOptions
  ): Promise<{ buffer: Buffer; width: number; height: number }> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    const page = await this.browser.newPage();
    
    try {
      // Set viewport
      const width = options.width || 1920;
      const height = options.height || 1080;
      const scale = options.scale || 1;
      
      await page.setViewport({ 
        width: Math.floor(width / scale), 
        height: Math.floor(height / scale),
        deviceScaleFactor: scale
      });

      // Create HTML content with Mermaid
      const htmlContent = this.createMermaidHTML(mermaidContent, options);
      await page.setContent(htmlContent, { waitUntil: 'networkidle0' });

      // Wait for Mermaid to render
      await page.waitForSelector('#mermaid-diagram svg', { timeout: 30000 });

      // Get the SVG element
      const svgElement = await page.$('#mermaid-diagram svg');
      if (!svgElement) {
        throw new Error('Failed to find rendered Mermaid diagram');
      }

      // Get SVG dimensions
      const boundingBox = await svgElement.boundingBox();
      if (!boundingBox) {
        throw new Error('Failed to get diagram dimensions');
      }

      let buffer: Buffer;

      if (options.format === 'svg') {
        // For SVG, get the SVG content directly
        const svgContent = await page.evaluate(() => {
          const svg = (globalThis as any).document.querySelector('#mermaid-diagram svg');
          return svg ? svg.outerHTML : '';
        });
        buffer = Buffer.from(svgContent, 'utf8');
      } else {
        // For other formats, take a screenshot
        const screenshotOptions: any = {
          type: options.format === 'jpg' || options.format === 'jpeg' ? 'jpeg' : 'png',
          omitBackground: options.backgroundColor === 'transparent'
        };

        if (options.format === 'jpg' || options.format === 'jpeg') {
          screenshotOptions.quality = options.quality || 90;
        }

        const screenshotBuffer = await svgElement.screenshot(screenshotOptions);
        buffer = Buffer.from(screenshotBuffer);

        // Process with Sharp if needed
        if (options.format === 'jpg' || options.format === 'jpeg') {
          const sharpInstance = sharp(buffer);
          
          if (options.backgroundColor && options.backgroundColor !== 'transparent') {
            sharpInstance.flatten({ background: options.backgroundColor });
          }
          
          buffer = await sharpInstance
            .jpeg({ quality: options.quality || 90 })
            .toBuffer();
        }
      }

      return {
        buffer,
        width: Math.round(boundingBox.width),
        height: Math.round(boundingBox.height)
      };

    } finally {
      await page.close();
    }
  }

  /**
   * Create HTML content for Mermaid rendering
   */
  private createMermaidHTML(mermaidContent: string, options: ConversionOptions): string {
    const theme = options.theme || 'default';
    const backgroundColor = options.backgroundColor || (theme === 'dark' ? '#1e1e1e' : '#ffffff');

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Mermaid Diagram</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: ${backgroundColor};
            font-family: 'Arial', sans-serif;
        }
        #mermaid-diagram {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div id="mermaid-diagram">
        <div class="mermaid">
${mermaidContent}
        </div>
    </div>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: '${theme}',
            themeVariables: {
                primaryColor: '#ff6b6b',
                primaryTextColor: '#333',
                primaryBorderColor: '#ff6b6b',
                lineColor: '#333',
                secondaryColor: '#4ecdc4',
                tertiaryColor: '#ffe66d'
            }
        });
    </script>
</body>
</html>`;
  }

  /**
   * Generate output file path based on input and options
   */
  private generateOutputPath(inputPath: string, options: ConversionOptions): string {
    const inputDir = path.dirname(inputPath);
    const inputName = path.basename(inputPath, path.extname(inputPath));
    const outputDir = options.outputDir || inputDir;
    const extension = options.format === 'jpg' ? 'jpg' : options.format;
    
    return path.join(outputDir, `${inputName}.${extension}`);
  }
}
