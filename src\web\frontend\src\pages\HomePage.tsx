import React from 'react';
import { Link } from 'react-router-dom';

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            ArchitekAI
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Transform natural language descriptions into professional architecture diagrams. 
            Powered by AI, designed for developers and architects.
          </p>
          <div className="flex gap-4 justify-center">
            <Link
              to="/generate"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Generate Diagram
            </Link>
            <Link
              to="/templates"
              className="bg-white hover:bg-gray-50 text-blue-600 border border-blue-600 px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Browse Templates
            </Link>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-blue-600 text-3xl mb-4">🤖</div>
            <h3 className="text-xl font-semibold mb-3">AI-Powered Analysis</h3>
            <p className="text-gray-600">
              Advanced NLP engine understands your architecture descriptions and 
              automatically identifies components and relationships.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-blue-600 text-3xl mb-4">📊</div>
            <h3 className="text-xl font-semibold mb-3">Multiple Formats</h3>
            <p className="text-gray-600">
              Generate diagrams in Mermaid, PlantUML, ASCII, Draw.io, and more. 
              Choose the format that works best for your workflow.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="text-blue-600 text-3xl mb-4">⚡</div>
            <h3 className="text-xl font-semibold mb-3">Lightning Fast</h3>
            <p className="text-gray-600">
              Generate professional architecture diagrams in seconds, not hours. 
              Perfect for rapid prototyping and documentation.
            </p>
          </div>
        </div>

        {/* Example Section */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-center mb-8">See It In Action</h2>
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-lg font-semibold mb-4">Input Description:</h3>
              <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
                "microservices architecture with API gateway, user service, and payment service"
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Generated Diagram:</h3>
              <div className="bg-gray-100 p-4 rounded-lg">
                <div className="text-xs text-gray-600 mb-2">Mermaid Diagram Preview</div>
                <div className="bg-white p-3 rounded border text-xs font-mono">
                  graph TD<br/>
                  &nbsp;&nbsp;A[API Gateway] --&gt; B[User Service]<br/>
                  &nbsp;&nbsp;A --&gt; C[Payment Service]<br/>
                  &nbsp;&nbsp;B --&gt; D[(User DB)]<br/>
                  &nbsp;&nbsp;C --&gt; E[(Payment DB)]
                </div>
              </div>
            </div>
          </div>
          <div className="text-center mt-8">
            <Link
              to="/generate"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors"
            >
              Try It Now
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
