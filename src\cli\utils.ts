/**
 * CLI Utilities for ArchitekAI
 */

import fs from 'fs-extra';
import path from 'path';
import chalk from 'chalk';
import figlet from 'figlet';
import boxen from 'boxen';
import ora, { Ora } from 'ora';
import { createLogger } from '@/utils/logger';

const logger = createLogger('CLI-Utils');

/**
 * Get the current version of ArchitekAI
 */
export function getVersion(): string {
  try {
    const packagePath = path.join(__dirname, '../../package.json');
    const packageJson = fs.readJsonSync(packagePath);
    return packageJson.version || '1.0.0';
  } catch (error) {
    logger.warn('Could not read version from package.json:', error);
    return '1.0.0';
  }
}

/**
 * Display the ArchitekAI banner
 */
export function displayBanner(): void {
  try {
    const banner = figlet.textSync('ArchitekAI', {
      font: 'ANSI Shadow',
      horizontalLayout: 'default',
      verticalLayout: 'default',
    });

    const version = getVersion();
    const subtitle = 'Intelligent Architecture Diagram Generator';
    
    const boxContent = `${chalk.cyan(banner)}\n\n${chalk.yellow(subtitle)}\n${chalk.gray(`Version ${version}`)}`;
    
    const box = boxen(boxContent, {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'cyan',
      backgroundColor: 'black',
    });

    console.log(box);
  } catch (error) {
    // Fallback if figlet fails
    console.log(chalk.cyan.bold('\n🏗️  ArchitekAI'));
    console.log(chalk.yellow('Intelligent Architecture Diagram Generator'));
    console.log(chalk.gray(`Version ${getVersion()}\n`));
  }
}

/**
 * Handle and display errors in a user-friendly way
 */
export function handleError(error: unknown): void {
  if (error instanceof Error) {
    if (error.name === 'ValidationError') {
      console.error(chalk.red('❌ Validation Error:'), error.message);
    } else if (error.name === 'ConfigurationError') {
      console.error(chalk.red('⚙️  Configuration Error:'), error.message);
    } else if (error.name === 'NetworkError') {
      console.error(chalk.red('🌐 Network Error:'), error.message);
      console.error(chalk.gray('Please check your internet connection and try again.'));
    } else if (error.name === 'FileSystemError') {
      console.error(chalk.red('📁 File System Error:'), error.message);
    } else {
      console.error(chalk.red('💥 Unexpected Error:'), error.message);
      
      if (process.env.NODE_ENV === 'development') {
        console.error(chalk.gray('\nStack trace:'));
        console.error(chalk.gray(error.stack));
      } else {
        console.error(chalk.gray('\nFor more details, run with --verbose flag.'));
      }
    }
  } else {
    console.error(chalk.red('💥 Unknown Error:'), String(error));
  }

  console.error(chalk.gray('\nIf this problem persists, please report it at:'));
  console.error(chalk.blue('https://github.com/HectorTa1989/ArchitekAI/issues'));
}

/**
 * Create a spinner for long-running operations
 */
export function createSpinner(text: string): Ora {
  return ora({
    text: chalk.cyan(text),
    spinner: 'dots',
    color: 'cyan',
  });
}

/**
 * Display success message with icon
 */
export function displaySuccess(message: string): void {
  console.log(chalk.green('✅'), message);
}

/**
 * Display warning message with icon
 */
export function displayWarning(message: string): void {
  console.log(chalk.yellow('⚠️ '), message);
}

/**
 * Display info message with icon
 */
export function displayInfo(message: string): void {
  console.log(chalk.blue('ℹ️ '), message);
}

/**
 * Display error message with icon
 */
export function displayError(message: string): void {
  console.log(chalk.red('❌'), message);
}

/**
 * Format file size in human-readable format
 */
export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const size = (bytes / Math.pow(1024, i)).toFixed(2);
  
  return `${size} ${sizes[i] || 'Bytes'}`;
}

/**
 * Format duration in human-readable format
 */
export function formatDuration(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  
  const seconds = Math.floor(milliseconds / 1000);
  const ms = milliseconds % 1000;
  
  if (seconds < 60) {
    return ms > 0 ? `${seconds}.${Math.floor(ms / 100)}s` : `${seconds}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  return `${minutes}m ${remainingSeconds}s`;
}

/**
 * Validate file path and ensure directory exists
 */
export async function ensureOutputPath(filePath: string): Promise<string> {
  try {
    const absolutePath = path.resolve(filePath);
    const directory = path.dirname(absolutePath);
    
    await fs.ensureDir(directory);
    
    return absolutePath;
  } catch (error) {
    throw new Error(`Failed to ensure output path: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Check if a file exists and is readable
 */
export async function isFileReadable(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath, fs.constants.R_OK);
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if a directory exists and is writable
 */
export async function isDirectoryWritable(dirPath: string): Promise<boolean> {
  try {
    await fs.access(dirPath, fs.constants.W_OK);
    return true;
  } catch {
    return false;
  }
}

/**
 * Truncate text to specified length with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Create a progress bar for batch operations
 */
export function createProgressBar(total: number): {
  update: (current: number, message?: string) => void;
  complete: (message?: string) => void;
} {
  let spinner: Ora | null = null;
  
  return {
    update: (current: number, message?: string) => {
      const percentage = Math.round((current / total) * 100);
      const progressText = `${message || 'Processing'} (${current}/${total}) ${percentage}%`;
      
      if (!spinner) {
        spinner = createSpinner(progressText);
        spinner.start();
      } else {
        spinner.text = chalk.cyan(progressText);
      }
    },
    
    complete: (message?: string) => {
      if (spinner) {
        spinner.succeed(chalk.green(message || 'Completed'));
        spinner = null;
      }
    },
  };
}

/**
 * Prompt user for confirmation
 */
export async function confirmAction(message: string, defaultValue = false): Promise<boolean> {
  const { default: inquirer } = await import('inquirer');
  
  const { confirmed } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'confirmed',
      message: chalk.yellow(message),
      default: defaultValue,
    },
  ]);
  
  return confirmed as boolean;
}

/**
 * Display a table of data
 */
export function displayTable(data: Array<Record<string, string | number>>, headers?: string[]): void {
  if (data.length === 0) {
    displayInfo('No data to display');
    return;
  }
  
  const keys = headers || Object.keys(data[0]!);
  const columnWidths = keys.map(key => 
    Math.max(key.length, ...data.map(row => String(row[key] || '').length))
  );
  
  // Header
  const headerRow = keys.map((key, i) => 
    chalk.bold(key.padEnd(columnWidths[i]!))
  ).join(' | ');
  
  console.log(headerRow);
  console.log(keys.map((_, i) => '-'.repeat(columnWidths[i]!)).join('-|-'));
  
  // Data rows
  data.forEach(row => {
    const dataRow = keys.map((key, i) => 
      String(row[key] || '').padEnd(columnWidths[i]!)
    ).join(' | ');
    
    console.log(dataRow);
  });
}

/**
 * Get user's home directory
 */
export function getHomeDirectory(): string {
  return process.env.HOME || process.env.USERPROFILE || process.cwd();
}

/**
 * Get ArchitekAI config directory
 */
export function getConfigDirectory(): string {
  const homeDir = getHomeDirectory();
  return path.join(homeDir, '.architekAI');
}

/**
 * Check if running in CI environment
 */
export function isCI(): boolean {
  return !!(
    process.env.CI ||
    process.env.CONTINUOUS_INTEGRATION ||
    process.env.BUILD_NUMBER ||
    process.env.GITHUB_ACTIONS ||
    process.env.GITLAB_CI ||
    process.env.CIRCLECI ||
    process.env.TRAVIS
  );
}
