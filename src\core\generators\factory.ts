/**
 * Generator Factory for ArchitekAI
 * 
 * Creates and manages different diagram generators.
 */

import { Generator } from '@/types/generator';
import { OutputFormat } from '@/types/architecture';
import { MermaidGenerator } from './mermaid';
import { createLogger } from '@/utils/logger';

const logger = createLogger('GeneratorFactory');

export class GeneratorFactory {
  private generators: Map<OutputFormat, () => Generator>;
  private instances: Map<OutputFormat, Generator>;

  constructor() {
    this.generators = new Map();
    this.instances = new Map();
    
    this.registerDefaultGenerators();
  }

  /**
   * Create a generator for the specified format
   */
  createGenerator(format: OutputFormat): Generator {
    // Return cached instance if available
    if (this.instances.has(format)) {
      return this.instances.get(format)!;
    }

    // Create new instance
    const generatorFactory = this.generators.get(format);
    if (!generatorFactory) {
      throw new Error(`No generator available for format: ${format}`);
    }

    const generator = generatorFactory();
    this.instances.set(format, generator);
    
    logger.debug(`Created generator for format: ${format}`);
    
    return generator;
  }

  /**
   * Register a generator for a specific format
   */
  registerGenerator(format: OutputFormat, generatorFactory: () => Generator): void {
    this.generators.set(format, generatorFactory);
    
    // Clear cached instance if it exists
    if (this.instances.has(format)) {
      this.instances.delete(format);
    }
    
    logger.debug(`Registered generator for format: ${format}`);
  }

  /**
   * Get all supported formats
   */
  getSupportedFormats(): OutputFormat[] {
    return Array.from(this.generators.keys());
  }

  /**
   * Check if a format is supported
   */
  isFormatSupported(format: OutputFormat): boolean {
    return this.generators.has(format);
  }

  /**
   * Get generator information for a format
   */
  getGeneratorInfo(format: OutputFormat): { name: string; version: string; description: string } | null {
    if (!this.isFormatSupported(format)) {
      return null;
    }

    const generator = this.createGenerator(format);
    return {
      name: generator.name,
      version: generator.version,
      description: generator.description,
    };
  }

  /**
   * Get all generator information
   */
  getAllGeneratorInfo(): Array<{ format: OutputFormat; name: string; version: string; description: string }> {
    return this.getSupportedFormats().map(format => {
      const info = this.getGeneratorInfo(format)!;
      return {
        format,
        ...info,
      };
    });
  }

  /**
   * Clear all cached generator instances
   */
  clearCache(): void {
    this.instances.clear();
    logger.debug('Cleared generator cache');
  }

  /**
   * Register default generators
   */
  private registerDefaultGenerators(): void {
    // Mermaid generator
    this.registerGenerator(OutputFormat.MERMAID, () => new MermaidGenerator());
    
    // PlantUML generator (placeholder)
    this.registerGenerator(OutputFormat.PLANTUML, () => new PlantUMLGenerator());
    
    // ASCII generator (placeholder)
    this.registerGenerator(OutputFormat.ASCII, () => new ASCIIGenerator());
    
    // Draw.io generator (placeholder)
    this.registerGenerator(OutputFormat.DRAWIO, () => new DrawIOGenerator());
    
    // Lucidchart generator (placeholder)
    this.registerGenerator(OutputFormat.LUCIDCHART, () => new LucidchartGenerator());
    
    logger.debug('Registered default generators');
  }
}

// Placeholder generators - these would be implemented in separate files

class PlantUMLGenerator implements Generator {
  readonly format = OutputFormat.PLANTUML;
  readonly name = 'PlantUML Generator';
  readonly version = '1.0.0';
  readonly description = 'Generates PlantUML diagrams from architecture descriptions';
  readonly supportedFeatures: any[] = [];

  async generate(architecture: any, options?: any): Promise<any> {
    const startTime = Date.now();
    
    // Basic PlantUML generation
    let content = '@startuml\n';
    content += `!theme plain\n`;
    content += `title ${architecture.name}\n\n`;
    
    // Add components
    architecture.components.forEach((component: any) => {
      const componentType = this.getPlantUMLComponentType(component.type);
      content += `${componentType} "${component.name}" as ${component.id}\n`;
    });
    
    content += '\n';
    
    // Add connections
    architecture.connections.forEach((connection: any) => {
      const arrow = this.getPlantUMLArrow(connection.type);
      const label = connection.label ? ` : ${connection.label}` : '';
      content += `${connection.source} ${arrow} ${connection.target}${label}\n`;
    });
    
    content += '@enduml\n';
    
    const processingTime = Date.now() - startTime;
    
    return {
      content,
      metadata: {
        format: this.format,
        generator: this.name,
        version: this.version,
        timestamp: new Date(),
        architecture: {
          id: architecture.id,
          name: architecture.name,
          componentCount: architecture.components.length,
          connectionCount: architecture.connections.length,
        },
        options: options || {},
      },
      warnings: [],
      statistics: {
        processingTime,
        outputSize: Buffer.byteLength(content, 'utf8'),
        complexity: {
          cyclomatic: 1,
          cognitive: 1,
          structural: architecture.components.length + architecture.connections.length,
          maintainability: 85,
        },
      },
    };
  }

  async validate(_architecture: any): Promise<any> {
    return {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };
  }

  getSchema(): any {
    return {
      format: this.format,
      version: this.version,
      options: [],
      features: [],
      examples: [],
    };
  }

  private getPlantUMLComponentType(type: string): string {
    const types: Record<string, string> = {
      service: 'component',
      database: 'database',
      api_gateway: 'interface',
      load_balancer: 'node',
      cache: 'storage',
      queue: 'queue',
      function: 'artifact',
    };
    return types[type] || 'component';
  }

  private getPlantUMLArrow(type: string): string {
    const arrows: Record<string, string> = {
      http: '-->',
      https: '==>',
      database_connection: '..>',
      message_queue: '-.->',
      api_call: '-->',
    };
    return arrows[type] || '-->';
  }
}

class ASCIIGenerator implements Generator {
  readonly format = OutputFormat.ASCII;
  readonly name = 'ASCII Generator';
  readonly version = '1.0.0';
  readonly description = 'Generates ASCII art diagrams from architecture descriptions';
  readonly supportedFeatures: any[] = [];

  async generate(architecture: any, options?: any): Promise<any> {
    const startTime = Date.now();
    
    // Simple ASCII generation
    let content = `${architecture.name}\n`;
    content += '='.repeat(architecture.name.length) + '\n\n';
    
    // List components
    content += 'Components:\n';
    architecture.components.forEach((component: any, index: number) => {
      content += `  ${index + 1}. [${component.name}] (${component.type})\n`;
    });
    
    content += '\nConnections:\n';
    architecture.connections.forEach((connection: any, index: number) => {
      const sourceName = architecture.components.find((c: any) => c.id === connection.source)?.name || connection.source;
      const targetName = architecture.components.find((c: any) => c.id === connection.target)?.name || connection.target;
      content += `  ${index + 1}. ${sourceName} --> ${targetName}\n`;
    });
    
    // Simple ASCII diagram
    content += '\nDiagram:\n';
    content += '┌─────────────┐\n';
    content += '│ Architecture │\n';
    content += '└─────────────┘\n';
    
    const processingTime = Date.now() - startTime;
    
    return {
      content,
      metadata: {
        format: this.format,
        generator: this.name,
        version: this.version,
        timestamp: new Date(),
        architecture: {
          id: architecture.id,
          name: architecture.name,
          componentCount: architecture.components.length,
          connectionCount: architecture.connections.length,
        },
        options: options || {},
      },
      warnings: [],
      statistics: {
        processingTime,
        outputSize: Buffer.byteLength(content, 'utf8'),
        complexity: {
          cyclomatic: 1,
          cognitive: 1,
          structural: architecture.components.length + architecture.connections.length,
          maintainability: 90,
        },
      },
    };
  }

  async validate(_architecture: any): Promise<any> {
    return {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };
  }

  getSchema(): any {
    return {
      format: this.format,
      version: this.version,
      options: [],
      features: [],
      examples: [],
    };
  }
}

class DrawIOGenerator implements Generator {
  readonly format = OutputFormat.DRAWIO;
  readonly name = 'Draw.io Generator';
  readonly version = '1.0.0';
  readonly description = 'Generates Draw.io XML diagrams from architecture descriptions';
  readonly supportedFeatures: any[] = [];

  async generate(architecture: any, options?: any): Promise<any> {
    const startTime = Date.now();
    
    // Basic Draw.io XML structure
    const content = `<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="${new Date().toISOString()}" agent="ArchitekAI" version="1.0.0">
  <diagram name="${architecture.name}" id="architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        ${this.generateDrawIOComponents(architecture.components)}
        ${this.generateDrawIOConnections(architecture.connections)}
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>`;
    
    const processingTime = Date.now() - startTime;
    
    return {
      content,
      metadata: {
        format: this.format,
        generator: this.name,
        version: this.version,
        timestamp: new Date(),
        architecture: {
          id: architecture.id,
          name: architecture.name,
          componentCount: architecture.components.length,
          connectionCount: architecture.connections.length,
        },
        options: options || {},
      },
      warnings: [],
      statistics: {
        processingTime,
        outputSize: Buffer.byteLength(content, 'utf8'),
        complexity: {
          cyclomatic: 1,
          cognitive: 1,
          structural: architecture.components.length + architecture.connections.length,
          maintainability: 80,
        },
      },
    };
  }

  async validate(_architecture: any): Promise<any> {
    return {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };
  }

  getSchema(): any {
    return {
      format: this.format,
      version: this.version,
      options: [],
      features: [],
      examples: [],
    };
  }

  private generateDrawIOComponents(components: any[]): string {
    return components.map((component, index) => {
      const x = 100 + (index % 4) * 200;
      const y = 100 + Math.floor(index / 4) * 150;
      return `        <mxCell id="${component.id}" value="${component.name}" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="${x}" y="${y}" width="120" height="60" as="geometry" />
        </mxCell>`;
    }).join('\n');
  }

  private generateDrawIOConnections(connections: any[]): string {
    return connections.map((connection, index) => {
      return `        <mxCell id="edge${index}" edge="1" parent="1" source="${connection.source}" target="${connection.target}">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>`;
    }).join('\n');
  }
}

class LucidchartGenerator implements Generator {
  readonly format = OutputFormat.LUCIDCHART;
  readonly name = 'Lucidchart Generator';
  readonly version = '1.0.0';
  readonly description = 'Generates Lucidchart JSON diagrams from architecture descriptions';
  readonly supportedFeatures: any[] = [];

  async generate(architecture: any, options?: any): Promise<any> {
    const startTime = Date.now();
    
    // Basic Lucidchart JSON structure
    const lucidchartData = {
      version: '1.0.0',
      generator: 'ArchitekAI',
      timestamp: new Date().toISOString(),
      diagram: {
        name: architecture.name,
        description: architecture.description,
        components: architecture.components.map((component: any, index: number) => ({
          id: component.id,
          name: component.name,
          type: component.type,
          position: {
            x: 100 + (index % 4) * 200,
            y: 100 + Math.floor(index / 4) * 150,
          },
          size: {
            width: 120,
            height: 60,
          },
          style: {
            backgroundColor: '#f0f0f0',
            borderColor: '#333333',
            textColor: '#000000',
          },
        })),
        connections: architecture.connections.map((connection: any) => ({
          id: connection.id,
          source: connection.source,
          target: connection.target,
          type: connection.type,
          label: connection.label,
          style: {
            lineColor: '#333333',
            lineWidth: 2,
          },
        })),
      },
    };
    
    const content = JSON.stringify(lucidchartData, null, 2);
    const processingTime = Date.now() - startTime;
    
    return {
      content,
      metadata: {
        format: this.format,
        generator: this.name,
        version: this.version,
        timestamp: new Date(),
        architecture: {
          id: architecture.id,
          name: architecture.name,
          componentCount: architecture.components.length,
          connectionCount: architecture.connections.length,
        },
        options: options || {},
      },
      warnings: [],
      statistics: {
        processingTime,
        outputSize: Buffer.byteLength(content, 'utf8'),
        complexity: {
          cyclomatic: 1,
          cognitive: 1,
          structural: architecture.components.length + architecture.connections.length,
          maintainability: 85,
        },
      },
    };
  }

  async validate(_architecture: any): Promise<any> {
    return {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: [],
    };
  }

  getSchema(): any {
    return {
      format: this.format,
      version: this.version,
      options: [],
      features: [],
      examples: [],
    };
  }
}
