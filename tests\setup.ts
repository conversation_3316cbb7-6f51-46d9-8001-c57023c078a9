/**
 * Test setup for ArchitekAI
 * 
 * Global test configuration and utilities.
 */

import { jest } from '@jest/globals';
import path from 'path';
import fs from 'fs-extra';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error';

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };

beforeAll(() => {
  // Mock console methods
  console.log = jest.fn();
  console.info = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  // Restore console methods
  Object.assign(console, originalConsole);
});

// Global test utilities
export const testUtils = {
  /**
   * Create a temporary directory for test files
   */
  async createTempDir(prefix = 'architekAI-test'): Promise<string> {
    const tempDir = path.join(process.cwd(), 'tmp', `${prefix}-${Date.now()}`);
    await fs.ensureDir(tempDir);
    return tempDir;
  },

  /**
   * Clean up temporary directory
   */
  async cleanupTempDir(dirPath: string): Promise<void> {
    if (await fs.pathExists(dirPath)) {
      await fs.remove(dirPath);
    }
  },

  /**
   * Create a test configuration file
   */
  async createTestConfig(dirPath: string, config: any): Promise<string> {
    const configPath = path.join(dirPath, '.architekAI.yaml');
    const yaml = await import('yaml');
    await fs.writeFile(configPath, yaml.stringify(config), 'utf8');
    return configPath;
  },

  /**
   * Create a test description file
   */
  async createTestDescription(dirPath: string, description: string, filename = 'test.txt'): Promise<string> {
    const filePath = path.join(dirPath, filename);
    await fs.writeFile(filePath, description, 'utf8');
    return filePath;
  },

  /**
   * Mock CLI arguments
   */
  mockArgv(args: string[]): void {
    process.argv = ['node', 'architekAI', ...args];
  },

  /**
   * Restore original CLI arguments
   */
  restoreArgv(): void {
    process.argv = process.argv.slice(0, 2);
  },

  /**
   * Wait for a specified amount of time
   */
  async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Generate a random string
   */
  randomString(length = 8): string {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Create a mock architecture object
   */
  createMockArchitecture(): any {
    return {
      id: 'test-arch',
      name: 'Test Architecture',
      description: 'A test architecture for unit testing',
      components: [
        {
          id: 'comp1',
          name: 'Component 1',
          type: 'service',
          description: 'Test component 1',
        },
        {
          id: 'comp2',
          name: 'Component 2',
          type: 'database',
          description: 'Test component 2',
        },
      ],
      connections: [
        {
          id: 'conn1',
          source: 'comp1',
          target: 'comp2',
          type: 'database_connection',
          label: 'Data Access',
        },
      ],
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        version: '1.0.0',
      },
    };
  },

  /**
   * Create a mock NLP parse result
   */
  createMockParseResult(): any {
    return {
      originalText: 'microservices with API gateway and user service',
      extractedComponents: [
        {
          name: 'API Gateway',
          type: 'api_gateway',
          confidence: 0.9,
          context: ['gateway', 'api'],
          properties: {},
        },
        {
          name: 'User Service',
          type: 'service',
          confidence: 0.8,
          context: ['user', 'service'],
          properties: {},
        },
      ],
      extractedConnections: [
        {
          source: 'API Gateway',
          target: 'User Service',
          type: 'http',
          confidence: 0.7,
          context: ['gateway', 'service'],
          properties: {},
        },
      ],
      suggestedPattern: 'microservices',
      confidence: 0.85,
      metadata: {
        processingTime: 150,
        nlpVersion: '1.0.0',
        patterns: ['microservices'],
        keywords: ['api', 'gateway', 'user', 'service'],
        entities: [],
      },
      architecture: testUtils.createMockArchitecture(),
    };
  },

  /**
   * Create a mock generator result
   */
  createMockGeneratorResult(): any {
    return {
      content: 'graph TD\n    A[API Gateway] --> B[User Service]',
      metadata: {
        format: 'mermaid',
        generator: 'MermaidGenerator',
        version: '1.0.0',
        timestamp: new Date(),
        architecture: {
          id: 'test-arch',
          name: 'Test Architecture',
          componentCount: 2,
          connectionCount: 1,
        },
        options: {},
      },
      warnings: [],
      statistics: {
        processingTime: 100,
        outputSize: 45,
        complexity: {
          cyclomatic: 1,
          cognitive: 1,
          structural: 2,
          maintainability: 85,
        },
      },
    };
  },
};

// Export for use in tests
export default testUtils;
