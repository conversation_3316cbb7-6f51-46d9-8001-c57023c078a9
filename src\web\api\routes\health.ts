/**
 * Health Check API Routes for ArchitekAI
 * 
 * Provides health status and system information endpoints.
 */

import { Router, Request, Response } from 'express';
import { createLogger } from '@/utils/logger';

const logger = createLogger('HealthRoutes');

export function healthRoutes(): Router {
  const router = Router();

  /**
   * GET /health
   * Basic health check
   */
  router.get('/', (_req: Request, res: Response) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  });

  /**
   * GET /health/detailed
   * Detailed health information
   */
  router.get('/detailed', async (_req: Request, res: Response) => {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      const healthData = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        system: {
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version,
          pid: process.pid,
        },
        memory: {
          rss: formatBytes(memoryUsage.rss),
          heapTotal: formatBytes(memoryUsage.heapTotal),
          heapUsed: formatBytes(memoryUsage.heapUsed),
          external: formatBytes(memoryUsage.external),
          arrayBuffers: formatBytes(memoryUsage.arrayBuffers),
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
        services: await checkServices(),
      };

      res.json(healthData);

    } catch (error) {
      logger.error('Health check failed:', error);
      res.status(500).json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
      });
    }
  });

  /**
   * GET /health/ready
   * Readiness probe for Kubernetes
   */
  router.get('/ready', async (_req: Request, res: Response) => {
    try {
      const services = await checkServices();
      const allHealthy = Object.values(services).every(service => service.status === 'healthy');

      if (allHealthy) {
        res.json({
          status: 'ready',
          timestamp: new Date().toISOString(),
          services,
        });
      } else {
        res.status(503).json({
          status: 'not ready',
          timestamp: new Date().toISOString(),
          services,
        });
      }

    } catch (error) {
      logger.error('Readiness check failed:', error);
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        error: 'Readiness check failed',
      });
    }
  });

  /**
   * GET /health/live
   * Liveness probe for Kubernetes
   */
  router.get('/live', (_req: Request, res: Response) => {
    // Simple liveness check - if we can respond, we're alive
    res.json({
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  });

  return router;
}

/**
 * Check the health of various services
 */
async function checkServices(): Promise<Record<string, any>> {
  const services: Record<string, any> = {};

  // Check NLP service
  try {
    // This would normally check if the NLP engine is responsive
    services.nlp = {
      status: 'healthy',
      message: 'NLP engine is operational',
      lastCheck: new Date().toISOString(),
    };
  } catch (error) {
    services.nlp = {
      status: 'unhealthy',
      message: 'NLP engine is not responding',
      error: error instanceof Error ? error.message : 'Unknown error',
      lastCheck: new Date().toISOString(),
    };
  }

  // Check template manager
  try {
    services.templates = {
      status: 'healthy',
      message: 'Template manager is operational',
      lastCheck: new Date().toISOString(),
    };
  } catch (error) {
    services.templates = {
      status: 'unhealthy',
      message: 'Template manager is not responding',
      error: error instanceof Error ? error.message : 'Unknown error',
      lastCheck: new Date().toISOString(),
    };
  }

  // Check generators
  try {
    services.generators = {
      status: 'healthy',
      message: 'Diagram generators are operational',
      lastCheck: new Date().toISOString(),
    };
  } catch (error) {
    services.generators = {
      status: 'unhealthy',
      message: 'Diagram generators are not responding',
      error: error instanceof Error ? error.message : 'Unknown error',
      lastCheck: new Date().toISOString(),
    };
  }

  // Check file system access
  try {
    const fs = await import('fs-extra');
    await fs.access('./templates');
    services.filesystem = {
      status: 'healthy',
      message: 'File system access is working',
      lastCheck: new Date().toISOString(),
    };
  } catch (error) {
    services.filesystem = {
      status: 'unhealthy',
      message: 'File system access failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      lastCheck: new Date().toISOString(),
    };
  }

  return services;
}

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const size = (bytes / Math.pow(1024, i)).toFixed(2);
  
  return `${size} ${sizes[i] || 'Bytes'}`;
}
