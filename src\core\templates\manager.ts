/**
 * Template Manager for ArchitekAI
 * 
 * Manages architecture templates including loading, validation, and application.
 */

import fs from 'fs-extra';
import path from 'path';
import yaml from 'yaml';
import { 
  ArchitectureTemplate, 
  Architecture, 
  Component, 
  Connection, 
  ArchitecturePattern,
  ComponentType,
  ConnectionType
} from '@/types/architecture';
import { TemplateConfig } from '@/types/config';
import { createLogger } from '@/utils/logger';

const logger = createLogger('TemplateManager');

export class TemplateManager {
  private config: TemplateConfig;
  private templates: Map<string, ArchitectureTemplate>;
  private initialized: boolean = false;

  constructor(config: TemplateConfig) {
    this.config = config;
    this.templates = new Map();
  }

  /**
   * Initialize the template manager
   */
  async initialize(): Promise<void> {
    try {
      logger.debug('Initializing template manager...');
      logger.debug(`Template config:`, {
        directory: this.config.directory,
        autoLoad: this.config.autoLoad,
        validation: this.config.validation,
        customTemplates: this.config.customTemplates
      });

      if (this.config.autoLoad) {
        await this.loadTemplatesFromDirectory(this.config.directory);

        // Load custom templates
        for (const customPath of this.config.customTemplates) {
          await this.loadTemplate(customPath);
        }
      } else {
        logger.debug('Template autoLoad is disabled');
      }

      this.initialized = true;
      logger.info(`Template manager initialized with ${this.templates.size} templates`);

    } catch (error) {
      logger.error('Failed to initialize template manager:', error);
      throw new Error(`Template manager initialization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get all available templates
   */
  getTemplates(): ArchitectureTemplate[] {
    this.ensureInitialized();
    return Array.from(this.templates.values());
  }

  /**
   * Get a specific template by ID
   */
  async getTemplate(id: string): Promise<ArchitectureTemplate> {
    this.ensureInitialized();
    
    const template = this.templates.get(id);
    if (!template) {
      throw new Error(`Template not found: ${id}`);
    }
    
    return template;
  }

  /**
   * Get templates by pattern
   */
  getTemplatesByPattern(pattern: ArchitecturePattern): ArchitectureTemplate[] {
    this.ensureInitialized();
    
    return this.getTemplates().filter(template => template.pattern === pattern);
  }

  /**
   * Get templates by complexity
   */
  getTemplatesByComplexity(complexity: 'simple' | 'moderate' | 'complex' | 'expert'): ArchitectureTemplate[] {
    this.ensureInitialized();
    
    return this.getTemplates().filter(template => template.metadata.complexity === complexity);
  }

  /**
   * Search templates by tags
   */
  searchTemplatesByTags(tags: string[]): ArchitectureTemplate[] {
    this.ensureInitialized();
    
    return this.getTemplates().filter(template => 
      tags.some(tag => template.metadata.tags.includes(tag.toLowerCase()))
    );
  }

  /**
   * Apply a template to create an architecture
   */
  async applyTemplate(template: ArchitectureTemplate, baseArchitecture?: Architecture): Promise<Architecture> {
    try {
      logger.debug(`Applying template: ${template.name}`);
      
      // Create components from template
      const components: Component[] = template.components.map((templateComp, index) => ({
        id: templateComp.id || `comp-${index + 1}`,
        name: templateComp.name || `Component ${index + 1}`,
        type: templateComp.type || ComponentType.SERVICE,
        description: templateComp.description || `${templateComp.name} component`,
        properties: { ...templateComp.properties },
        position: templateComp.position || {
          x: 100 + (index % 4) * 200,
          y: 100 + Math.floor(index / 4) * 150,
        },
        metadata: {
          tags: ['template', template.id],
          category: this.getComponentCategory(templateComp.type || ComponentType.SERVICE),
          template: template.id,
        },
      }));

      // Create connections from template
      const connections: Connection[] = template.connections.map((templateConn, index) => ({
        id: templateConn.id || `conn-${index + 1}`,
        source: templateConn.source || '',
        target: templateConn.target || '',
        type: templateConn.type || ConnectionType.HTTP,
        label: templateConn.label || this.generateConnectionLabel(templateConn.type || ConnectionType.HTTP),
        properties: { ...templateConn.properties },
        bidirectional: templateConn.bidirectional || false,
      }));

      // Merge with base architecture if provided
      if (baseArchitecture) {
        components.push(...baseArchitecture.components);
        connections.push(...baseArchitecture.connections);
      }

      const architecture: Architecture = {
        id: `arch-${Date.now()}`,
        name: baseArchitecture?.name || `${template.name} Architecture`,
        description: baseArchitecture?.description || template.description,
        components,
        connections,
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          version: '1.0.0',
          template: template.id,
          style: {
            theme: 'light',
            colors: this.getDefaultColorScheme(),
            fonts: this.getDefaultFontConfig(),
            layout: this.getDefaultLayoutConfig(),
          },
        },
      };

      logger.debug(`Template applied successfully: ${components.length} components, ${connections.length} connections`);
      
      return architecture;
      
    } catch (error) {
      logger.error('Failed to apply template:', error);
      throw new Error(`Template application failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Customize a template with user options
   */
  async customizeTemplate(
    template: ArchitectureTemplate, 
    customizations: Record<string, unknown>
  ): Promise<ArchitectureTemplate> {
    try {
      logger.debug(`Customizing template: ${template.name}`);
      
      const customizedTemplate = JSON.parse(JSON.stringify(template)); // Deep clone
      
      // Apply customizations based on schema
      for (const [key, value] of Object.entries(customizations)) {
        if (template.customization.configurationSchema[key]) {
          await this.applyCustomization(customizedTemplate, key, value);
        }
      }
      
      return customizedTemplate;
      
    } catch (error) {
      logger.error('Failed to customize template:', error);
      throw new Error(`Template customization failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Validate a template
   */
  async validateTemplate(template: ArchitectureTemplate): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    try {
      // Basic validation
      if (!template.id) {
        errors.push('Template must have an ID');
      }
      
      if (!template.name) {
        errors.push('Template must have a name');
      }
      
      if (!template.description) {
        errors.push('Template must have a description');
      }
      
      if (!template.pattern) {
        errors.push('Template must specify an architecture pattern');
      }
      
      // Validate components
      if (!template.components || template.components.length === 0) {
        errors.push('Template must have at least one component');
      }
      
      // Check for required components
      if (template.customization.requiredComponents) {
        const componentIds = template.components.map(c => c.id).filter(Boolean);
        for (const requiredId of template.customization.requiredComponents) {
          if (!componentIds.includes(requiredId)) {
            errors.push(`Required component missing: ${requiredId}`);
          }
        }
      }
      
      // Validate connections
      const componentIds = new Set(template.components.map(c => c.id).filter(Boolean));
      for (const connection of template.connections) {
        if (connection.source && !componentIds.has(connection.source)) {
          errors.push(`Connection references unknown source component: ${connection.source}`);
        }
        if (connection.target && !componentIds.has(connection.target)) {
          errors.push(`Connection references unknown target component: ${connection.target}`);
        }
      }
      
      return {
        isValid: errors.length === 0,
        errors,
      };
      
    } catch (error) {
      errors.push(`Validation error: ${error instanceof Error ? error.message : String(error)}`);
      return {
        isValid: false,
        errors,
      };
    }
  }

  /**
   * Save a template
   */
  async saveTemplate(template: ArchitectureTemplate, filePath?: string): Promise<void> {
    try {
      const validation = await this.validateTemplate(template);
      if (!validation.isValid) {
        throw new Error(`Template validation failed: ${validation.errors.join(', ')}`);
      }
      
      const outputPath = filePath || path.join(this.config.directory, `${template.id}.yaml`);
      await fs.ensureDir(path.dirname(outputPath));
      
      const yamlContent = yaml.stringify(template, {
        indent: 2,
        lineWidth: 120,
      });
      
      await fs.writeFile(outputPath, yamlContent, 'utf8');
      
      // Add to cache
      this.templates.set(template.id, template);
      
      logger.debug(`Template saved: ${outputPath}`);
      
    } catch (error) {
      logger.error('Failed to save template:', error);
      throw new Error(`Template save failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Delete a template
   */
  async deleteTemplate(id: string): Promise<void> {
    try {
      // const _template = await this.getTemplate(id); // TODO: Use template for validation
      
      // Remove from cache
      this.templates.delete(id);
      
      // Try to remove file if it exists in the templates directory
      const templatePath = path.join(this.config.directory, `${id}.yaml`);
      if (await fs.pathExists(templatePath)) {
        await fs.remove(templatePath);
        logger.debug(`Template file deleted: ${templatePath}`);
      }
      
      logger.debug(`Template deleted: ${id}`);
      
    } catch (error) {
      logger.error('Failed to delete template:', error);
      throw new Error(`Template deletion failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Load templates from directory
   */
  private async loadTemplatesFromDirectory(directory: string): Promise<void> {
    try {
      // Resolve the directory path relative to the current working directory
      const resolvedDirectory = path.resolve(directory);
      logger.debug(`Attempting to load templates from: ${resolvedDirectory}`);

      const dirExists = await fs.pathExists(resolvedDirectory);
      logger.debug(`Directory exists check: ${resolvedDirectory} = ${dirExists}`);

      if (!dirExists) {
        logger.warn(`Template directory does not exist: ${resolvedDirectory}`);
        return;
      }

      // Use fs.readdir instead of glob to avoid Windows path issues
      const files = await fs.readdir(resolvedDirectory);
      logger.debug(`Files in directory: ${files.join(', ')}`);

      const templateFiles = files
        .filter(file => {
          const ext = path.extname(file).toLowerCase();
          return ext === '.yaml' || ext === '.yml' || ext === '.json';
        })
        .map(file => path.join(resolvedDirectory, file));

      logger.debug(`Filtered template files: ${templateFiles.join(', ')}`);

      logger.debug(`Found ${templateFiles.length} template files in ${resolvedDirectory}`);

      for (const filePath of templateFiles) {
        try {
          await this.loadTemplate(filePath);
        } catch (error) {
          logger.warn(`Failed to load template from ${filePath}:`, error);
        }
      }

    } catch (error) {
      logger.error(`Failed to load templates from directory ${directory}:`, error);
    }
  }

  /**
   * Load a single template file
   */
  private async loadTemplate(filePath: string): Promise<void> {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const template = this.parseTemplate(content, filePath);
      
      if (this.config.validation) {
        const validation = await this.validateTemplate(template);
        if (!validation.isValid) {
          throw new Error(`Template validation failed: ${validation.errors.join(', ')}`);
        }
      }
      
      this.templates.set(template.id, template);
      logger.debug(`Loaded template: ${template.id} from ${filePath}`);
      
    } catch (error) {
      logger.error(`Failed to load template from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Parse template content
   */
  private parseTemplate(content: string, filePath: string): ArchitectureTemplate {
    const ext = path.extname(filePath).toLowerCase();
    
    try {
      let parsed: any;
      
      switch (ext) {
        case '.yaml':
        case '.yml':
          parsed = yaml.parse(content);
          break;
        case '.json':
          parsed = JSON.parse(content);
          break;
        default:
          throw new Error(`Unsupported template file format: ${ext}`);
      }
      
      // Ensure required fields have defaults
      return {
        id: parsed.id || path.basename(filePath, ext),
        name: parsed.name || 'Unnamed Template',
        description: parsed.description || 'No description provided',
        pattern: parsed.pattern || ArchitecturePattern.CUSTOM,
        components: parsed.components || [],
        connections: parsed.connections || [],
        metadata: {
          version: parsed.metadata?.version || '1.0.0',
          author: parsed.metadata?.author || 'Unknown',
          tags: parsed.metadata?.tags || [],
          complexity: parsed.metadata?.complexity || 'moderate',
          useCases: parsed.metadata?.useCases || [],
          prerequisites: parsed.metadata?.prerequisites || [],
          documentation: parsed.metadata?.documentation || '',
        },
        customization: {
          allowComponentAddition: parsed.customization?.allowComponentAddition ?? true,
          allowComponentRemoval: parsed.customization?.allowComponentRemoval ?? true,
          allowConnectionModification: parsed.customization?.allowConnectionModification ?? true,
          requiredComponents: parsed.customization?.requiredComponents || [],
          optionalComponents: parsed.customization?.optionalComponents || [],
          configurationSchema: parsed.customization?.configurationSchema || {},
        },
      };
      
    } catch (error) {
      throw new Error(`Failed to parse template ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Apply a single customization
   */
  private async applyCustomization(
    template: ArchitectureTemplate, 
    key: string, 
    value: unknown
  ): Promise<void> {
    const schema = template.customization.configurationSchema[key];
    if (!schema) return;
    
    switch (key) {
      case 'serviceCount':
        if (typeof value === 'number') {
          await this.adjustServiceCount(template, value);
        }
        break;
        
      case 'databaseType':
        if (typeof value === 'string') {
          await this.changeDatabaseType(template, value);
        }
        break;
        
      case 'messageQueue':
        if (typeof value === 'boolean') {
          await this.toggleMessageQueue(template, value);
        }
        break;
        
      default:
        logger.debug(`Unknown customization key: ${key}`);
    }
  }

  /**
   * Adjust the number of services in a template
   */
  private async adjustServiceCount(template: ArchitectureTemplate, count: number): Promise<void> {
    const services = template.components.filter(c => c.type === ComponentType.SERVICE);
    const currentCount = services.length;
    
    if (count > currentCount) {
      // Add more services
      for (let i = currentCount; i < count; i++) {
        template.components.push({
          id: `service-${i + 1}`,
          name: `Service ${i + 1}`,
          type: ComponentType.SERVICE,
          description: `Auto-generated service ${i + 1}`,
        });
      }
    } else if (count < currentCount) {
      // Remove services (keep required ones)
      const requiredServices = template.customization.requiredComponents.filter(id => 
        services.some(s => s.id === id)
      );
      
      const servicesToRemove = services.slice(count).filter(s => 
        !requiredServices.includes(s.id!)
      );
      
      servicesToRemove.forEach(service => {
        const index = template.components.findIndex(c => c.id === service.id);
        if (index !== -1) {
          template.components.splice(index, 1);
        }
      });
    }
  }

  /**
   * Change database type in template
   */
  private async changeDatabaseType(template: ArchitectureTemplate, dbType: string): Promise<void> {
    template.components.forEach(component => {
      if (component.type === ComponentType.DATABASE) {
        component.properties = {
          ...component.properties,
          type: dbType,
        };
      }
    });
  }

  /**
   * Toggle message queue in template
   */
  private async toggleMessageQueue(template: ArchitectureTemplate, enabled: boolean): Promise<void> {
    const hasQueue = template.components.some(c => c.type === ComponentType.QUEUE);
    
    if (enabled && !hasQueue) {
      // Add message queue
      template.components.push({
        id: 'message-queue',
        name: 'Message Queue',
        type: ComponentType.QUEUE,
        description: 'Message queue for async communication',
      });
    } else if (!enabled && hasQueue) {
      // Remove message queue
      const queueIndex = template.components.findIndex(c => c.type === ComponentType.QUEUE);
      if (queueIndex !== -1) {
        template.components.splice(queueIndex, 1);
      }
      
      // Remove related connections
      template.connections = template.connections.filter(conn => 
        !template.components.some(c => c.type === ComponentType.QUEUE && (c.id === conn.source || c.id === conn.target))
      );
    }
  }

  /**
   * Helper methods
   */
  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error('Template manager not initialized. Call initialize() first.');
    }
  }

  private getComponentCategory(type: ComponentType): string {
    const categories: Record<ComponentType, string> = {
      [ComponentType.SERVICE]: 'compute',
      [ComponentType.DATABASE]: 'storage',
      [ComponentType.API_GATEWAY]: 'networking',
      [ComponentType.LOAD_BALANCER]: 'networking',
      [ComponentType.CACHE]: 'storage',
      [ComponentType.QUEUE]: 'messaging',
      [ComponentType.EVENT_STREAM]: 'messaging',
      [ComponentType.FUNCTION]: 'compute',
      [ComponentType.CDN]: 'networking',
      [ComponentType.WEB_APP]: 'presentation',
      [ComponentType.MOBILE_APP]: 'presentation',
      [ComponentType.STORAGE]: 'storage',
      [ComponentType.CONTAINER]: 'compute',
      [ComponentType.EXTERNAL_SERVICE]: 'external',
      [ComponentType.USER_INTERFACE]: 'presentation',
      [ComponentType.DESKTOP_APP]: 'presentation',
      [ComponentType.IOT_DEVICE]: 'edge',
      [ComponentType.NETWORK]: 'networking',
      [ComponentType.SECURITY]: 'security',
      [ComponentType.MONITORING]: 'observability',
      [ComponentType.LOGGING]: 'observability',
      [ComponentType.ANALYTICS]: 'analytics',
      [ComponentType.NOTIFICATION]: 'messaging',
      [ComponentType.WORKFLOW]: 'orchestration',
      [ComponentType.DATA_PIPELINE]: 'data',
      [ComponentType.ML_MODEL]: 'ai',
      [ComponentType.CUSTOM]: 'custom',
    };
    
    return categories[type] || 'unknown';
  }

  private generateConnectionLabel(type: ConnectionType): string {
    const labels: Record<ConnectionType, string> = {
      [ConnectionType.HTTP]: 'HTTP Request',
      [ConnectionType.HTTPS]: 'HTTPS Request',
      [ConnectionType.DATABASE_CONNECTION]: 'Data Access',
      [ConnectionType.MESSAGE_QUEUE]: 'Message',
      [ConnectionType.API_CALL]: 'API Call',
      [ConnectionType.TCP]: 'TCP Connection',
      [ConnectionType.UDP]: 'UDP Connection',
      [ConnectionType.WEBSOCKET]: 'WebSocket',
      [ConnectionType.GRPC]: 'gRPC Call',
      [ConnectionType.GRAPHQL]: 'GraphQL Query',
      [ConnectionType.REST]: 'REST API',
      [ConnectionType.SOAP]: 'SOAP Call',
      [ConnectionType.EVENT_STREAM]: 'Event Stream',
      [ConnectionType.FILE_SYSTEM]: 'File Access',
      [ConnectionType.NETWORK]: 'Network',
      [ConnectionType.VPN]: 'VPN Connection',
      [ConnectionType.DEPENDENCY]: 'Dependency',
      [ConnectionType.DATA_FLOW]: 'Data Flow',
      [ConnectionType.CONTROL_FLOW]: 'Control Flow',
      [ConnectionType.INHERITANCE]: 'Inherits',
      [ConnectionType.COMPOSITION]: 'Composed of',
      [ConnectionType.AGGREGATION]: 'Aggregates',
      [ConnectionType.ASSOCIATION]: 'Associated with',
      [ConnectionType.CUSTOM]: 'Custom Connection',
    };
    
    return labels[type] || 'Connection';
  }

  private getDefaultColorScheme(): any {
    return {
      primary: '#2196F3',
      secondary: '#FF9800',
      accent: '#4CAF50',
      background: '#FFFFFF',
      text: '#212121',
      border: '#E0E0E0',
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#F44336',
    };
  }

  private getDefaultFontConfig(): any {
    return {
      family: 'Arial, sans-serif',
      size: {
        small: 12,
        medium: 14,
        large: 16,
      },
      weight: {
        normal: 400,
        bold: 700,
      },
    };
  }

  private getDefaultLayoutConfig(): any {
    return {
      direction: 'horizontal',
      spacing: {
        component: 20,
        connection: 10,
        group: 30,
      },
      alignment: 'center',
    };
  }
}
