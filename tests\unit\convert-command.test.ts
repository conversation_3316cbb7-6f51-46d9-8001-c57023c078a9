/**
 * Tests for Convert CLI Command
 */

import { convertCommand } from '../../src/cli/commands/convert';
import { ConfigManager } from '../../src/core/config/manager';
import { Command } from 'commander';
import fs from 'fs-extra';
import path from 'path';
import os from 'os';

// Mock the MermaidConverter to avoid browser dependencies in tests
jest.mock('../../src/core/converter', () => ({
  MermaidConverter: jest.fn().mockImplementation(() => ({
    initialize: jest.fn().mockResolvedValue(undefined),
    cleanup: jest.fn().mockResolvedValue(undefined),
    convertFile: jest.fn().mockResolvedValue({
      success: true,
      inputFile: 'test.mmd',
      outputFile: 'test.jpg',
      format: 'jpg',
      size: 12345,
      width: 800,
      height: 600,
      duration: 1000,
    }),
    convertBatch: jest.fn().mockResolvedValue({
      results: [
        {
          success: true,
          inputFile: 'test1.mmd',
          outputFile: 'test1.jpg',
          format: 'jpg',
          size: 12345,
          width: 800,
          height: 600,
          duration: 1000,
        },
        {
          success: true,
          inputFile: 'test2.mmd',
          outputFile: 'test2.jpg',
          format: 'jpg',
          size: 12346,
          width: 800,
          height: 600,
          duration: 1100,
        },
      ],
      totalProcessed: 2,
      successful: 2,
      failed: 0,
      totalDuration: 2100,
      errors: [],
    }),
  })),
}));

describe('Convert Command', () => {
  let configManager: ConfigManager;
  let tempDir: string;
  let testMermaidFile: string;

  const sampleMermaidContent = `graph TD
    A[API Gateway] --> B[User Service]
    A --> C[Payment Service]
    B --> D[(User DB)]
    C --> E[(Payment DB)]`;

  beforeAll(async () => {
    // Create temporary directory for test files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'architek-ai-convert-test-'));
    testMermaidFile = path.join(tempDir, 'test-diagram.mmd');
    await fs.writeFile(testMermaidFile, sampleMermaidContent);
  });

  afterAll(async () => {
    // Clean up temporary directory
    await fs.remove(tempDir);
  });

  beforeEach(async () => {
    configManager = new ConfigManager();
    await configManager.initialize();
  });

  describe('command creation', () => {
    it('should create convert command with correct options', () => {
      const command = convertCommand(configManager);

      expect(command).toBeInstanceOf(Command);
      expect(command.name()).toBe('convert');
      expect(command.description()).toContain('Convert Mermaid');
    });

    it('should have all required options', () => {
      const command = convertCommand(configManager);
      const options = command.options;

      const optionNames = options.map(opt => opt.long);
      expect(optionNames).toContain('--input');
      expect(optionNames).toContain('--output');
      expect(optionNames).toContain('--format');
      expect(optionNames).toContain('--quality');
      expect(optionNames).toContain('--width');
      expect(optionNames).toContain('--height');
      expect(optionNames).toContain('--scale');
      expect(optionNames).toContain('--background');
      expect(optionNames).toContain('--theme');
      expect(optionNames).toContain('--batch');
      expect(optionNames).toContain('--overwrite');
      expect(optionNames).toContain('--verbose');
      expect(optionNames).toContain('--interactive');
    });
  });

  describe('option validation', () => {
    it('should have correct default values', () => {
      const command = convertCommand(configManager);
      const options = command.options;

      const formatOption = options.find(opt => opt.long === '--format');
      expect(formatOption?.defaultValue).toBe('jpg');

      const qualityOption = options.find(opt => opt.long === '--quality');
      expect(qualityOption?.defaultValue).toBe('90');

      const widthOption = options.find(opt => opt.long === '--width');
      expect(widthOption?.defaultValue).toBe('1920');

      const heightOption = options.find(opt => opt.long === '--height');
      expect(heightOption?.defaultValue).toBe('1080');

      const scaleOption = options.find(opt => opt.long === '--scale');
      expect(scaleOption?.defaultValue).toBe('1');

      const backgroundOption = options.find(opt => opt.long === '--background');
      expect(backgroundOption?.defaultValue).toBe('#ffffff');

      const themeOption = options.find(opt => opt.long === '--theme');
      expect(themeOption?.defaultValue).toBe('default');
    });
  });

  describe('help text', () => {
    it('should include usage examples', () => {
      const command = convertCommand(configManager);
      const helpText = command.helpInformation();

      expect(helpText).toContain('Examples:');
      expect(helpText).toContain('architek-ai convert -i diagram.mmd -f jpg');
      expect(helpText).toContain('architek-ai convert -i "*.mmd" --batch');
      expect(helpText).toContain('architek-ai convert --interactive');
    });

    it('should describe all options', () => {
      const command = convertCommand(configManager);
      const helpText = command.helpInformation();

      expect(helpText).toContain('input file or pattern');
      expect(helpText).toContain('output format');
      expect(helpText).toContain('image quality');
      expect(helpText).toContain('batch processing');
      expect(helpText).toContain('interactive mode');
    });
  });

  describe('command aliases', () => {
    it('should support short option aliases', () => {
      const command = convertCommand(configManager);
      const options = command.options;

      const inputOption = options.find(opt => opt.long === '--input');
      expect(inputOption?.short).toBe('-i');

      const outputOption = options.find(opt => opt.long === '--output');
      expect(outputOption?.short).toBe('-o');

      const formatOption = options.find(opt => opt.long === '--format');
      expect(formatOption?.short).toBe('-f');

      const qualityOption = options.find(opt => opt.long === '--quality');
      expect(qualityOption?.short).toBe('-q');

      const widthOption = options.find(opt => opt.long === '--width');
      expect(widthOption?.short).toBe('-w');

      const heightOption = options.find(opt => opt.long === '--height');
      expect(heightOption?.short).toBe('-h');

      const scaleOption = options.find(opt => opt.long === '--scale');
      expect(scaleOption?.short).toBe('-s');
    });
  });

  describe('format validation', () => {
    it('should accept valid formats', () => {
      const validFormats = ['jpg', 'jpeg', 'png', 'svg'];
      
      // This test would need to be implemented with actual command execution
      // For now, we just verify the formats are documented
      const command = convertCommand(configManager);
      const helpText = command.helpInformation();
      
      validFormats.forEach(format => {
        expect(helpText).toContain(format);
      });
    });
  });

  describe('integration with ConfigManager', () => {
    it('should use ConfigManager for default settings', () => {
      const command = convertCommand(configManager);
      
      // Verify that the command was created with the config manager
      expect(command).toBeDefined();
      expect(command.name()).toBe('convert');
    });

    it('should respect configuration defaults', async () => {
      // This would test that the command respects config file settings
      // Implementation would depend on how config is integrated
      const command = convertCommand(configManager);
      expect(command).toBeDefined();
    });
  });

  describe('error scenarios', () => {
    it('should handle missing input gracefully', () => {
      const command = convertCommand(configManager);
      
      // The actual error handling would be tested with command execution
      // For now, we verify the command structure supports error handling
      expect(command.options.find(opt => opt.long === '--input')).toBeDefined();
    });

    it('should handle invalid format gracefully', () => {
      const command = convertCommand(configManager);
      
      // Verify format option exists for validation
      const formatOption = command.options.find(opt => opt.long === '--format');
      expect(formatOption).toBeDefined();
    });
  });

  describe('batch processing', () => {
    it('should support batch flag', () => {
      const command = convertCommand(configManager);
      const batchOption = command.options.find(opt => opt.long === '--batch');
      
      expect(batchOption).toBeDefined();
      expect(batchOption?.description).toContain('batch');
    });
  });

  describe('interactive mode', () => {
    it('should support interactive flag', () => {
      const command = convertCommand(configManager);
      const interactiveOption = command.options.find(opt => opt.long === '--interactive');
      
      expect(interactiveOption).toBeDefined();
      expect(interactiveOption?.description).toContain('interactive');
    });
  });

  describe('output customization', () => {
    it('should support custom dimensions', () => {
      const command = convertCommand(configManager);
      
      const widthOption = command.options.find(opt => opt.long === '--width');
      const heightOption = command.options.find(opt => opt.long === '--height');
      
      expect(widthOption).toBeDefined();
      expect(heightOption).toBeDefined();
    });

    it('should support quality settings', () => {
      const command = convertCommand(configManager);
      const qualityOption = command.options.find(opt => opt.long === '--quality');
      
      expect(qualityOption).toBeDefined();
      expect(qualityOption?.description).toContain('quality');
    });

    it('should support theme selection', () => {
      const command = convertCommand(configManager);
      const themeOption = command.options.find(opt => opt.long === '--theme');
      
      expect(themeOption).toBeDefined();
      expect(themeOption?.description).toContain('theme');
    });

    it('should support background color customization', () => {
      const command = convertCommand(configManager);
      const backgroundOption = command.options.find(opt => opt.long === '--background');
      
      expect(backgroundOption).toBeDefined();
      expect(backgroundOption?.description).toContain('background');
    });
  });
});
