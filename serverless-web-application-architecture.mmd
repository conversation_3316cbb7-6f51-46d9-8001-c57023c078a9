graph TD
    external[External Clients]
    api_gateway{{API Gateway}}
    auth_function(Authentication Function)
    user_function(User Management Function)
    order_function(Order Processing Function)
    notification_function(Notification Function)
    event_queue>Event Queue]
    user_database[(User Database)]
    file_storage[(File Storage)]
    cdn{Content Delivery Network}
    comp_1{{Gateway}}
    comp_2[Service]
    comp_3[(Database)]
    comp_4[(Storage)]
    comp_5(Function)
    comp_6(Lambda)
    comp_7(Serverless)
    comp_8{Cdn}
    comp_9[Web App]

    external ==>|Static Content| cdn
    external ==>|API Requests| api_gateway
    api_gateway -->|Auth Requests| auth_function
    api_gateway -->|User Requests| user_function
    api_gateway -->|Order Requests| order_function
    order_function -.->|Order Events| event_queue
    event_queue -.->|Notification Events| notification_function
    user_function -.->|User Data| user_database
    order_function -->|File Operations| file_storage
    cdn -->|Origin Requests| file_storage
    comp_2 -.->|Data Access| comp_3
    comp_2 -.->|Data Access| comp_4
