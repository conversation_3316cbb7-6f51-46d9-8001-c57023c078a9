/**
 * Configuration types for ArchitekAI
 */

export interface AppConfig {
  version: string;
  environment: Environment;
  logging: LoggingConfig;
  output: OutputConfig;
  nlp: NLPConfig;
  templates: TemplateConfig;
  web: WebConfig;
  cli: CLIConfig;
  plugins: PluginConfig;
}

export interface LoggingConfig {
  level: LogLevel;
  format: LogFormat;
  outputs: LogOutput[];
  file?: FileLogConfig;
  console?: ConsoleLogConfig;
}

export interface OutputConfig {
  defaultFormat: OutputFormat;
  directory: string;
  filename: string;
  overwrite: boolean;
  backup: boolean;
  compression: boolean;
  formats: FormatConfig[];
}

export interface NLPConfig {
  engine: NLPEngine;
  confidence: {
    minimum: number;
    component: number;
    connection: number;
    pattern: number;
  };
  preprocessing: PreprocessingConfig;
  postprocessing: PostprocessingConfig;
  customPatterns: CustomPattern[];
}

export interface TemplateConfig {
  directory: string;
  autoLoad: boolean;
  validation: boolean;
  customTemplates: string[];
  defaultTemplate?: string;
}

export interface WebConfig {
  enabled: boolean;
  port: number;
  host: string;
  cors: CORSConfig;
  security: SecurityConfig;
  rateLimit: RateLimitConfig;
  static: StaticConfig;
}

export interface CLIConfig {
  interactive: boolean;
  colors: boolean;
  progress: boolean;
  verbose: boolean;
  confirmations: boolean;
  shortcuts: Record<string, string>;
}

export interface PluginConfig {
  enabled: boolean;
  directory: string;
  autoLoad: boolean;
  whitelist: string[];
  blacklist: string[];
}

export interface FileLogConfig {
  path: string;
  maxSize: string;
  maxFiles: number;
  compress: boolean;
}

export interface ConsoleLogConfig {
  colors: boolean;
  timestamp: boolean;
  level: boolean;
}

export interface FormatConfig {
  format: OutputFormat;
  enabled: boolean;
  options: Record<string, unknown>;
  template?: string;
}

export interface PreprocessingConfig {
  normalize: boolean;
  removeStopWords: boolean;
  stemming: boolean;
  lemmatization: boolean;
  customFilters: string[];
}

export interface PostprocessingConfig {
  validation: boolean;
  optimization: boolean;
  beautification: boolean;
  customProcessors: string[];
}

export interface CustomPattern {
  name: string;
  pattern: string;
  type: 'component' | 'connection' | 'pattern';
  confidence: number;
  metadata: Record<string, unknown>;
}

export interface CORSConfig {
  enabled: boolean;
  origin: string | string[];
  methods: string[];
  allowedHeaders: string[];
  credentials: boolean;
}

export interface SecurityConfig {
  helmet: boolean;
  rateLimit: boolean;
  authentication: AuthConfig;
  authorization: AuthzConfig;
}

export interface AuthConfig {
  enabled: boolean;
  type: 'jwt' | 'oauth' | 'apikey' | 'basic';
  secret?: string;
  expiration?: string;
  issuer?: string;
}

export interface AuthzConfig {
  enabled: boolean;
  roles: string[];
  permissions: Permission[];
}

export interface Permission {
  resource: string;
  actions: string[];
  roles: string[];
}

export interface RateLimitConfig {
  enabled: boolean;
  windowMs: number;
  max: number;
  message: string;
  standardHeaders: boolean;
  legacyHeaders: boolean;
}

export interface StaticConfig {
  enabled: boolean;
  directory: string;
  maxAge: number;
  etag: boolean;
  index: string[];
}

export enum Environment {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  HTTP = 'http',
  VERBOSE = 'verbose',
  DEBUG = 'debug',
  SILLY = 'silly',
}

export enum LogFormat {
  JSON = 'json',
  SIMPLE = 'simple',
  COMBINED = 'combined',
  COMMON = 'common',
  DEV = 'dev',
  SHORT = 'short',
  TINY = 'tiny',
}

export enum LogOutput {
  CONSOLE = 'console',
  FILE = 'file',
  HTTP = 'http',
  STREAM = 'stream',
}

export enum OutputFormat {
  MERMAID = 'mermaid',
  PLANTUML = 'plantuml',
  ASCII = 'ascii',
  DRAWIO = 'drawio',
  LUCIDCHART = 'lucidchart',
  JSON = 'json',
  YAML = 'yaml',
  SVG = 'svg',
  PNG = 'png',
  PDF = 'pdf',
}

export enum NLPEngine {
  NATURAL = 'natural',
  COMPROMISE = 'compromise',
  CUSTOM = 'custom',
}

export interface UserConfig extends Partial<AppConfig> {
  // User-specific overrides
  user?: {
    name: string;
    email: string;
    preferences: UserPreferences;
  };
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  notifications: NotificationPreferences;
  shortcuts: Record<string, string>;
}

export interface NotificationPreferences {
  enabled: boolean;
  types: NotificationType[];
  channels: NotificationChannel[];
}

export enum NotificationType {
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  INFO = 'info',
  PROGRESS = 'progress',
}

export enum NotificationChannel {
  CONSOLE = 'console',
  EMAIL = 'email',
  WEBHOOK = 'webhook',
  DESKTOP = 'desktop',
}

export interface ProjectConfig extends Partial<AppConfig> {
  // Project-specific configuration
  project?: {
    name: string;
    version: string;
    description: string;
    repository?: string;
    author?: string;
    license?: string;
  };
}

export interface ConfigValidationResult {
  isValid: boolean;
  errors: ConfigError[];
  warnings: ConfigWarning[];
}

export interface ConfigError {
  path: string;
  message: string;
  value: unknown;
  expected: string;
}

export interface ConfigWarning {
  path: string;
  message: string;
  suggestion: string;
}
