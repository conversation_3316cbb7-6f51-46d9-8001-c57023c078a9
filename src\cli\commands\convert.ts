/**
 * Convert Command for ArchitekAI CLI
 * 
 * Handles conversion of Mermaid (.mmd) files to various image formats.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import path from 'path';
import { glob } from 'glob';
import inquirer from 'inquirer';
import { ConfigManager } from '@/core/config/manager';
import { MermaidConverter, ConversionOptions } from '@/core/converter';
import { createLogger } from '@/utils/logger';
import { 
  createSpinner,
  createProgressBar,
  displayError,
  displayInfo,
  displaySuccess,
  displayWarning,
  ensureOutputPath,
  formatDuration,
  formatFileSize,
  confirmAction,
} from '../utils';

const logger = createLogger('ConvertCommand');

interface ConvertCommandOptions {
  input?: string;
  output?: string;
  format?: 'jpg' | 'jpeg' | 'png' | 'svg' | 'pdf';
  quality?: number;
  width?: number;
  height?: number;
  scale?: number;
  backgroundColor?: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  batch?: boolean;
  overwrite?: boolean;
  verbose?: boolean;
  interactive?: boolean;
}

interface InputFile {
  path: string;
  name: string;
  size: number;
}

export function convertCommand(configManager: ConfigManager): Command {
  const command = new Command('convert');

  command
    .description('Convert Mermaid (.mmd) files to image formats (JPG, PNG, SVG)')
    .option('-i, --input <path>', 'input file or pattern (e.g., "*.mmd", "diagram.mmd")')
    .option('-o, --output <path>', 'output directory or file path')
    .option('-f, --format <format>', 'output format (jpg, png, svg)', 'jpg')
    .option('-q, --quality <number>', 'image quality for JPEG (1-100)', '90')
    .option('-w, --width <number>', 'output width in pixels', '1920')
    .option('-h, --height <number>', 'output height in pixels', '1080')
    .option('-s, --scale <number>', 'device scale factor', '1')
    .option('--background <color>', 'background color (hex, rgb, or named color)', '#ffffff')
    .option('--theme <theme>', 'mermaid theme (default, dark, forest, neutral)', 'default')
    .option('--batch', 'enable batch processing of multiple files')
    .option('--overwrite', 'overwrite existing files without confirmation')
    .option('--verbose', 'enable verbose output')
    .option('--interactive', 'enable interactive mode for options')
    .addHelpText('after', `
Examples:
  ${chalk.cyan('architek-ai convert -i diagram.mmd -f jpg')}
  ${chalk.cyan('architek-ai convert -i "*.mmd" --batch -o ./images/ -f png')}
  ${chalk.cyan('architek-ai convert -i diagram.mmd -f jpg -q 95 -w 2560 -h 1440')}
  ${chalk.cyan('architek-ai convert --interactive')}
  ${chalk.cyan('architek-ai convert -i diagram.mmd -f svg --theme dark')}
    `)
    .action(async (options: ConvertCommandOptions) => {
      await executeConvert(options, configManager);
    });

  return command;
}

async function executeConvert(
  options: ConvertCommandOptions, 
  configManager: ConfigManager
): Promise<void> {
  const startTime = Date.now();
  let spinner = createSpinner('Initializing converter...');
  
  try {
    spinner.start();

    // Get configuration
    const config = configManager.getConfig();
    
    // Merge options with config defaults
    const mergedOptions = {
      format: options.format || 'jpg',
      quality: parseInt(String(options.quality || 90)),
      width: parseInt(String(options.width || 1920)),
      height: parseInt(String(options.height || 1080)),
      scale: parseFloat(String(options.scale || 1)),
      backgroundColor: options.backgroundColor || '#ffffff',
      theme: options.theme || 'default',
      batch: options.batch || false,
      overwrite: options.overwrite || false,
      verbose: options.verbose ?? config.cli.verbose,
      interactive: options.interactive ?? config.cli.interactive,
      ...options,
    };

    // Interactive mode
    if (mergedOptions.interactive && !options.input) {
      spinner.stop();
      const interactiveOptions = await promptForConversionOptions();
      Object.assign(mergedOptions, interactiveOptions);
      spinner = createSpinner('Processing files...');
      spinner.start();
    }

    // Validate input
    if (!mergedOptions.input) {
      spinner.fail('Input file or pattern is required');
      displayError('Please specify an input file with -i or use --interactive mode');
      process.exit(1);
    }

    // Find input files
    spinner.text = 'Finding input files...';
    const inputFiles = await findInputFiles(mergedOptions);

    if (inputFiles.length === 0) {
      spinner.fail('No input files found');
      displayError(`No .mmd files found matching: ${mergedOptions.input}`);
      process.exit(1);
    }

    displayInfo(`Found ${inputFiles.length} file(s) to convert`);

    // Confirm batch operation if multiple files
    if (inputFiles.length > 1 && !mergedOptions.batch) {
      spinner.stop();
      const shouldContinue = await confirmAction(
        `Found ${inputFiles.length} files. Enable batch mode to process all?`,
        true
      );
      if (!shouldContinue) {
        displayInfo('Operation cancelled');
        process.exit(0);
      }
      mergedOptions.batch = true;
      spinner = createSpinner('Initializing converter...');
      spinner.start();
    }

    // Initialize converter
    spinner.text = 'Initializing Mermaid converter...';
    const converter = new MermaidConverter();
    await converter.initialize();

    // Prepare conversion options
    const conversionOptions: ConversionOptions = {
      format: mergedOptions.format as 'jpg' | 'jpeg' | 'png' | 'svg' | 'pdf',
      quality: mergedOptions.quality,
      width: mergedOptions.width,
      height: mergedOptions.height,
      scale: mergedOptions.scale,
      backgroundColor: mergedOptions.backgroundColor,
      theme: mergedOptions.theme as 'default' | 'dark' | 'forest' | 'neutral',
      outputDir: mergedOptions.output,
      overwrite: mergedOptions.overwrite,
    };

    spinner.stop();

    // Process conversions
    if (mergedOptions.batch && inputFiles.length > 1) {
      const result = await converter.convertBatch(
        inputFiles.map(f => f.path), 
        conversionOptions
      );
      await displayBatchResults(result);
    } else {
      const result = await converter.convertFile(inputFiles[0].path, conversionOptions);
      await displaySingleResult(result);
    }

    // Cleanup
    await converter.cleanup();

    const totalDuration = Date.now() - startTime;
    displaySuccess(`Conversion completed in ${formatDuration(totalDuration)}`);

  } catch (error) {
    if (spinner) {
      spinner.fail('Conversion failed');
    }
    
    logger.error('Convert command failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

async function promptForConversionOptions(): Promise<Partial<ConvertCommandOptions>> {
  const questions = [
    {
      type: 'input',
      name: 'input',
      message: 'Input file or pattern:',
      default: '*.mmd',
      validate: (input: string) => input.trim().length > 0 || 'Input is required',
    },
    {
      type: 'input',
      name: 'output',
      message: 'Output directory (optional):',
      default: './images',
    },
    {
      type: 'list',
      name: 'format',
      message: 'Output format:',
      choices: [
        { name: 'JPEG (.jpg)', value: 'jpg' },
        { name: 'PNG (.png)', value: 'png' },
        { name: 'SVG (.svg)', value: 'svg' },
      ],
      default: 'jpg',
    },
    {
      type: 'number',
      name: 'quality',
      message: 'JPEG quality (1-100):',
      default: 90,
      when: (answers: any) => answers.format === 'jpg',
      validate: (input: number) => (input >= 1 && input <= 100) || 'Quality must be between 1 and 100',
    },
    {
      type: 'list',
      name: 'theme',
      message: 'Mermaid theme:',
      choices: [
        { name: 'Default', value: 'default' },
        { name: 'Dark', value: 'dark' },
        { name: 'Forest', value: 'forest' },
        { name: 'Neutral', value: 'neutral' },
      ],
      default: 'default',
    },
    {
      type: 'confirm',
      name: 'batch',
      message: 'Enable batch processing?',
      default: true,
    },
    {
      type: 'confirm',
      name: 'overwrite',
      message: 'Overwrite existing files?',
      default: false,
    },
  ];

  return await inquirer.prompt(questions);
}

async function findInputFiles(options: ConvertCommandOptions): Promise<InputFile[]> {
  const fs = await import('fs-extra');
  const inputPattern = options.input!;
  
  let filePaths: string[];
  
  if (path.extname(inputPattern)) {
    // Single file
    filePaths = [inputPattern];
  } else {
    // Pattern matching
    filePaths = await glob(inputPattern, {
      ignore: ['node_modules/**', 'dist/**', '.git/**'],
    });
  }

  const inputFiles: InputFile[] = [];
  
  for (const filePath of filePaths) {
    try {
      if (await fs.pathExists(filePath)) {
        const stats = await fs.stat(filePath);
        if (stats.isFile() && path.extname(filePath).toLowerCase() === '.mmd') {
          inputFiles.push({
            path: filePath,
            name: path.basename(filePath),
            size: stats.size,
          });
        }
      }
    } catch (error) {
      logger.warn(`Skipping file ${filePath}:`, error);
    }
  }

  return inputFiles;
}

async function displaySingleResult(result: any): Promise<void> {
  if (result.success) {
    displaySuccess(`✅ Converted: ${result.inputFile}`);
    displayInfo(`   Output: ${result.outputFile}`);
    displayInfo(`   Format: ${result.format.toUpperCase()}`);
    displayInfo(`   Size: ${formatFileSize(result.size)}`);
    displayInfo(`   Dimensions: ${result.width}x${result.height}px`);
    displayInfo(`   Duration: ${formatDuration(result.duration)}`);
  } else {
    displayError(`❌ Failed: ${result.inputFile}`);
    displayError(`   Error: ${result.error}`);
  }
}

async function displayBatchResults(result: any): Promise<void> {
  const progressBar = createProgressBar(result.totalProcessed);
  progressBar.complete(`Processed ${result.totalProcessed} file(s)`);

  displayInfo(`\n📊 Batch Conversion Results:`);
  displayInfo(`   Total: ${result.totalProcessed}`);
  displaySuccess(`   Successful: ${result.successful}`);
  
  if (result.failed > 0) {
    displayError(`   Failed: ${result.failed}`);
  }
  
  displayInfo(`   Duration: ${formatDuration(result.totalDuration)}`);

  // Show successful conversions
  if (result.successful > 0) {
    displayInfo(`\n✅ Successful Conversions:`);
    result.results
      .filter((r: any) => r.success)
      .forEach((r: any) => {
        displayInfo(`   ${r.inputFile} → ${r.outputFile} (${formatFileSize(r.size)})`);
      });
  }

  // Show errors
  if (result.errors.length > 0) {
    displayWarning(`\n❌ Errors:`);
    result.errors.forEach((error: any) => {
      displayError(`   ${error.file}: ${error.error}`);
    });
  }
}
