/**
 * Template Command for ArchitekAI CLI
 * 
 * Handles template management operations including listing, using, and customizing templates.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import { ConfigManager } from '@/core/config/manager';
import { TemplateManager } from '@/core/templates/manager';
// Note: GeneratorFactory import will be implemented in subsequent tasks
// import { GeneratorFactory } from '@/core/generators/factory';
import { ArchitecturePattern, OutputFormat } from '@/types/architecture';
import { createLogger } from '@/utils/logger';
import { 
  createSpinner, 
  displaySuccess, 
  displayError, 
  displayInfo,
  displayTable,
  confirmAction,
} from '../utils';

const logger = createLogger('TemplateCommand');

interface TemplateListOptions {
  pattern?: string;
  complexity?: string;
  format?: 'table' | 'json' | 'yaml';
  verbose?: boolean;
}

interface TemplateUseOptions {
  customize?: boolean;
  output?: string;
  format?: OutputFormat;
  interactive?: boolean;
  preview?: boolean;
}

interface TemplateCreateOptions {
  name: string;
  description?: string;
  pattern?: ArchitecturePattern;
  file?: string;
  interactive?: boolean;
}

export function templateCommand(configManager: ConfigManager): Command {
  const command = new Command('template');

  command
    .description('Manage architecture templates')
    .addHelpText('after', `
Examples:
  ${chalk.cyan('architekAI template list')}
  ${chalk.cyan('architekAI template list --pattern microservices')}
  ${chalk.cyan('architekAI template use microservices --customize')}
  ${chalk.cyan('architekAI template create --name "my-template" --interactive')}
  ${chalk.cyan('architekAI template show microservices')}
    `);

  // List templates subcommand
  command
    .command('list')
    .description('List available templates')
    .option('-p, --pattern <pattern>', 'filter by architecture pattern')
    .option('-c, --complexity <level>', 'filter by complexity level (simple, moderate, complex, expert)')
    .option('-f, --format <format>', 'output format (table, json, yaml)', 'table')
    .option('--verbose', 'show detailed information')
    .action(async (options: TemplateListOptions) => {
      await executeTemplateList(options, configManager);
    });

  // Use template subcommand
  command
    .command('use <name>')
    .description('Use a template to generate a diagram')
    .option('--customize', 'enable template customization')
    .option('-o, --output <path>', 'output file path')
    .option('-f, --format <format>', 'output format', 'mermaid')
    .option('-i, --interactive', 'enable interactive mode')
    .option('--preview', 'preview before saving')
    .action(async (name: string, options: TemplateUseOptions) => {
      await executeTemplateUse(name, options, configManager);
    });

  // Show template subcommand
  command
    .command('show <name>')
    .description('Show detailed information about a template')
    .action(async (name: string) => {
      await executeTemplateShow(name, configManager);
    });

  // Create template subcommand
  command
    .command('create')
    .description('Create a new custom template')
    .option('-n, --name <name>', 'template name')
    .option('-d, --description <desc>', 'template description')
    .option('-p, --pattern <pattern>', 'architecture pattern')
    .option('-f, --file <path>', 'load template from file')
    .option('-i, --interactive', 'interactive template creation')
    .action(async (options: TemplateCreateOptions) => {
      await executeTemplateCreate(options, configManager);
    });

  // Delete template subcommand
  command
    .command('delete <name>')
    .description('Delete a custom template')
    .action(async (name: string) => {
      await executeTemplateDelete(name, configManager);
    });

  return command;
}

async function executeTemplateList(options: TemplateListOptions, configManager: ConfigManager): Promise<void> {
  const spinner = createSpinner('Loading templates...');

  try {
    spinner.start();

    // Initialize TemplateManager
    const config = configManager.getConfig();
    const templateManager = new TemplateManager(config.templates);
    await templateManager.initialize();

    const templates = templateManager.getTemplates();

    // Convert to display format
    const displayTemplates = templates.map(template => ({
      name: template.name,
      pattern: template.pattern,
      complexity: template.metadata.complexity,
      componentCount: template.components.length,
      description: template.description,
    }));

    // Apply filters
    let filteredTemplates = displayTemplates;

    if (options.pattern) {
      filteredTemplates = filteredTemplates.filter(t =>
        t.pattern.toLowerCase().includes(options.pattern!.toLowerCase())
      );
    }

    if (options.complexity) {
      filteredTemplates = filteredTemplates.filter(t =>
        t.complexity === options.complexity
      );
    }

    spinner.stop();

    if (filteredTemplates.length === 0) {
      displayInfo('No templates found matching the criteria.');
      return;
    }

    displayInfo(`Found ${filteredTemplates.length} template(s):`);

    if (options.format === 'json') {
      console.log(JSON.stringify(filteredTemplates, null, 2));
    } else if (options.format === 'yaml') {
      const yaml = await import('yaml');
      console.log(yaml.stringify(filteredTemplates));
    } else {
      // Table format
      const tableData = filteredTemplates.map(template => ({
        Name: template.name,
        Pattern: template.pattern,
        Complexity: template.complexity,
        Components: template.componentCount,
        Description: options.verbose ? template.description : template.description.substring(0, 50) + '...',
      }));

      displayTable(tableData);
    }

  } catch (error) {
    spinner.fail('Failed to load templates');
    logger.error('Template list failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

async function executeTemplateUse(name: string, options: TemplateUseOptions, _configManager: ConfigManager): Promise<void> {
  let spinner = createSpinner(`Loading template: ${name}`);
  
  try {
    spinner.start();

    // TODO: Replace with actual TemplateManager when implemented
    const template = getMockTemplate(name);
    
    if (!template) {
      spinner.fail(`Template '${name}' not found`);
      displayError(`Template '${name}' does not exist. Use 'architekAI template list' to see available templates.`);
      process.exit(1);
    }

    spinner.text = 'Preparing template...';

    let customizedTemplate = template;

    // Template customization
    if (options.customize || options.interactive) {
      spinner.stop();
      customizedTemplate = await customizeTemplate(template);
      spinner = createSpinner('Generating diagram from template...');
      spinner.start();
    }

    // Generate diagram from template
    spinner.text = 'Generating diagram...';
    
    // TODO: Implement actual generation logic
    const diagramContent = generateMockDiagram(customizedTemplate, options.format || 'mermaid');

    // Preview
    if (options.preview) {
      spinner.stop();
      await previewTemplate(diagramContent, options.format || 'mermaid');
      
      const shouldSave = await confirmAction('Save the diagram?', true);
      if (!shouldSave) {
        displayInfo('Diagram not saved.');
        return;
      }
      
      spinner = createSpinner('Saving diagram...');
      spinner.start();
    }

    // Save result
    const outputPath = options.output || `${name}-diagram.${getFileExtension(options.format || 'mermaid')}`;
    await saveTemplateResult(diagramContent, outputPath);

    spinner.succeed('Template applied successfully!');
    
    displaySuccess(`Generated diagram from template: ${name}`);
    displayInfo(`Output: ${outputPath}`);
    displayInfo(`Components: ${customizedTemplate.componentCount}`);

  } catch (error) {
    spinner.fail('Failed to use template');
    logger.error('Template use failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

async function executeTemplateShow(name: string, _configManager: ConfigManager): Promise<void> {
  const spinner = createSpinner(`Loading template: ${name}`);
  
  try {
    spinner.start();

    // TODO: Replace with actual TemplateManager when implemented
    const template = getMockTemplate(name);
    
    if (!template) {
      spinner.fail(`Template '${name}' not found`);
      displayError(`Template '${name}' does not exist.`);
      process.exit(1);
    }

    spinner.stop();

    // Display template details
    console.log(chalk.cyan.bold(`\n📋 Template: ${template.name}`));
    console.log(chalk.gray('─'.repeat(50)));
    
    console.log(`${chalk.bold('Description:')} ${template.description}`);
    console.log(`${chalk.bold('Pattern:')} ${template.pattern}`);
    console.log(`${chalk.bold('Complexity:')} ${template.complexity}`);
    console.log(`${chalk.bold('Components:')} ${template.componentCount}`);
    console.log(`${chalk.bold('Use Cases:')} ${template.useCases.join(', ')}`);
    
    if (template.prerequisites.length > 0) {
      console.log(`${chalk.bold('Prerequisites:')} ${template.prerequisites.join(', ')}`);
    }

    console.log(`\n${chalk.bold('Components:')}`);
    template.components.forEach(component => {
      console.log(`  • ${chalk.cyan(component.name)} (${component.type})`);
    });

    console.log(`\n${chalk.bold('Usage:')}`);
    console.log(chalk.gray(`  architekAI template use ${name}`));
    console.log(chalk.gray(`  architekAI template use ${name} --customize`));

  } catch (error) {
    spinner.fail('Failed to show template');
    logger.error('Template show failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

async function executeTemplateCreate(options: TemplateCreateOptions, _configManager: ConfigManager): Promise<void> {
  try {
    let templateData = options;

    if (options.interactive || !options.name) {
      templateData = await promptForTemplateCreation(options);
    }

    const spinner = createSpinner('Creating template...');
    spinner.start();

    // TODO: Implement actual template creation
    displayInfo('Template creation feature coming soon!');
    
    spinner.succeed('Template created successfully!');
    displaySuccess(`Created template: ${templateData.name}`);

  } catch (error) {
    logger.error('Template create failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

async function executeTemplateDelete(name: string, _configManager: ConfigManager): Promise<void> {
  try {
    const confirmed = await confirmAction(`Are you sure you want to delete template '${name}'?`, false);
    
    if (!confirmed) {
      displayInfo('Template deletion cancelled.');
      return;
    }

    const spinner = createSpinner(`Deleting template: ${name}`);
    spinner.start();

    // TODO: Implement actual template deletion
    displayInfo('Template deletion feature coming soon!');
    
    spinner.succeed('Template deleted successfully!');
    displaySuccess(`Deleted template: ${name}`);

  } catch (error) {
    logger.error('Template delete failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

// Helper functions (mock implementations)

function getMockTemplates() {
  return [
    {
      name: 'microservices',
      pattern: 'microservices',
      complexity: 'moderate',
      componentCount: 8,
      description: 'Modern microservices architecture with API gateway, service discovery, and distributed data management',
      useCases: ['scalable web applications', 'distributed systems', 'cloud-native applications'],
      prerequisites: ['containerization knowledge', 'API design experience'],
      components: [
        { name: 'API Gateway', type: 'api_gateway' },
        { name: 'User Service', type: 'service' },
        { name: 'Order Service', type: 'service' },
        { name: 'Payment Service', type: 'service' },
        { name: 'User Database', type: 'database' },
        { name: 'Order Database', type: 'database' },
        { name: 'Message Queue', type: 'queue' },
        { name: 'Load Balancer', type: 'load_balancer' },
      ],
    },
    {
      name: 'monolithic',
      pattern: 'monolithic',
      complexity: 'simple',
      componentCount: 4,
      description: 'Traditional monolithic architecture with layered design and single database',
      useCases: ['small to medium applications', 'rapid prototyping', 'simple business logic'],
      prerequisites: ['basic web development'],
      components: [
        { name: 'Web Application', type: 'web_app' },
        { name: 'Application Server', type: 'service' },
        { name: 'Database', type: 'database' },
        { name: 'Load Balancer', type: 'load_balancer' },
      ],
    },
    {
      name: 'serverless',
      pattern: 'serverless',
      complexity: 'moderate',
      componentCount: 6,
      description: 'Event-driven serverless architecture with functions, queues, and managed services',
      useCases: ['event processing', 'API backends', 'data processing pipelines'],
      prerequisites: ['cloud platform knowledge', 'function-as-a-service experience'],
      components: [
        { name: 'API Gateway', type: 'api_gateway' },
        { name: 'Lambda Functions', type: 'function' },
        { name: 'Event Queue', type: 'queue' },
        { name: 'Database', type: 'database' },
        { name: 'Storage', type: 'storage' },
        { name: 'CDN', type: 'cdn' },
      ],
    },
  ];
}

function getMockTemplate(name: string) {
  return getMockTemplates().find(t => t.name === name);
}

async function customizeTemplate(template: any): Promise<any> {
  displayInfo('Template customization mode');
  
  const questions = [
    {
      type: 'confirm',
      name: 'addComponents',
      message: 'Add additional components?',
      default: false,
    },
    {
      type: 'confirm',
      name: 'modifyExisting',
      message: 'Modify existing components?',
      default: false,
    },
  ];

  const customizations = await inquirer.prompt(questions);
  
  if (customizations.addComponents || customizations.modifyExisting) {
    displayInfo('Template customization features coming soon!');
  }

  return template;
}

async function previewTemplate(content: string, _format: string): Promise<void> {
  console.log(chalk.cyan('\n--- Template Preview ---'));
  console.log(content.substring(0, 500));
  if (content.length > 500) {
    console.log(chalk.gray('... (truncated)'));
  }
  console.log(chalk.cyan('--- End Preview ---\n'));
}

function generateMockDiagram(template: any, _format: string): string {
  return `// Generated from template: ${template.name}
// Pattern: ${template.pattern}
// Components: ${template.componentCount}

graph TD
    A[API Gateway] --> B[User Service]
    A --> C[Order Service]
    B --> D[User Database]
    C --> E[Order Database]
`;
}

async function saveTemplateResult(content: string, outputPath: string): Promise<void> {
  const fs = await import('fs-extra');
  await fs.writeFile(outputPath, content, 'utf8');
}

function getFileExtension(format: string): string {
  const extensions: Record<string, string> = {
    mermaid: 'mmd',
    plantuml: 'puml',
    ascii: 'txt',
    drawio: 'drawio',
    lucidchart: 'json',
  };
  return extensions[format] || 'txt';
}

async function promptForTemplateCreation(options: TemplateCreateOptions): Promise<TemplateCreateOptions> {
  const questions = [
    {
      type: 'input',
      name: 'name',
      message: 'Template name:',
      when: !options.name,
      validate: (input: string) => input.trim() ? true : 'Name is required',
    },
    {
      type: 'input',
      name: 'description',
      message: 'Template description:',
      when: !options.description,
    },
    {
      type: 'list',
      name: 'pattern',
      message: 'Architecture pattern:',
      choices: Object.values(ArchitecturePattern),
      when: !options.pattern,
    },
  ];

  const answers = await inquirer.prompt(questions);
  return { ...options, ...answers };
}
