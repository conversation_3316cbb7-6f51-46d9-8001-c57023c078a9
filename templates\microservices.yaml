# Microservices Architecture Template
# A modern microservices architecture with API gateway, service discovery, and distributed data management

id: "microservices"
name: "Microservices Architecture"
description: "Modern microservices architecture with API gateway, service discovery, and distributed data management"
pattern: "microservices"
version: "1.0.0"
author: "ArchitekAI"

metadata:
  tags:
    - "microservices"
    - "distributed"
    - "scalable"
    - "cloud-native"
  complexity: "moderate"
  useCases:
    - "scalable web applications"
    - "distributed systems"
    - "cloud-native applications"
    - "enterprise applications"
  prerequisites:
    - "containerization knowledge"
    - "API design experience"
    - "distributed systems understanding"
  documentation: |
    This template provides a foundation for microservices architecture with:
    - API Gateway for external communication
    - Service discovery and registration
    - Distributed data management
    - Inter-service communication patterns
    - Load balancing and fault tolerance

# Component definitions
components:
  - id: "external"
    name: "External Clients"
    type: "external"
    description: "External clients, users, and applications"
    properties:
      type: "client"
    position:
      x: 100
      y: -50

  - id: "api-gateway"
    name: "API Gateway"
    type: "api_gateway"
    description: "Central entry point for all client requests"
    properties:
      port: 80
      protocols: ["HTTP", "HTTPS"]
      features: ["routing", "authentication", "rate-limiting"]
    position:
      x: 100
      y: 50

  - id: "service-discovery"
    name: "Service Discovery"
    type: "service"
    description: "Service registry and discovery mechanism"
    properties:
      type: "consul"
      port: 8500
    position:
      x: 300
      y: 50

  - id: "load-balancer"
    name: "Load Balancer"
    type: "load_balancer"
    description: "Distributes incoming requests across service instances"
    properties:
      algorithm: "round-robin"
      healthCheck: true
    position:
      x: 200
      y: 150

  - id: "user-service"
    name: "User Service"
    type: "service"
    description: "Handles user management and authentication"
    properties:
      port: 8001
      database: "user-db"
      endpoints: ["/users", "/auth"]
    position:
      x: 100
      y: 250

  - id: "order-service"
    name: "Order Service"
    type: "service"
    description: "Manages order processing and fulfillment"
    properties:
      port: 8002
      database: "order-db"
      endpoints: ["/orders", "/checkout"]
    position:
      x: 300
      y: 250

  - id: "payment-service"
    name: "Payment Service"
    type: "service"
    description: "Processes payments and billing"
    properties:
      port: 8003
      database: "payment-db"
      endpoints: ["/payments", "/billing"]
    position:
      x: 500
      y: 250

  - id: "notification-service"
    name: "Notification Service"
    type: "service"
    description: "Sends notifications via email, SMS, push"
    properties:
      port: 8004
      endpoints: ["/notify"]
    position:
      x: 700
      y: 250

  - id: "user-db"
    name: "User Database"
    type: "database"
    description: "Stores user data and profiles"
    properties:
      type: "postgresql"
      port: 5432
    position:
      x: 100
      y: 350

  - id: "order-db"
    name: "Order Database"
    type: "database"
    description: "Stores order and inventory data"
    properties:
      type: "postgresql"
      port: 5433
    position:
      x: 300
      y: 350

  - id: "payment-db"
    name: "Payment Database"
    type: "database"
    description: "Stores payment and transaction data"
    properties:
      type: "postgresql"
      port: 5434
    position:
      x: 500
      y: 350

  - id: "message-queue"
    name: "Message Queue"
    type: "queue"
    description: "Asynchronous communication between services"
    properties:
      type: "rabbitmq"
      port: 5672
    position:
      x: 400
      y: 150

# Connection definitions
connections:
  - id: "client-to-gateway"
    source: "external"
    target: "api-gateway"
    type: "https"
    label: "Client Requests"
    properties:
      protocol: "HTTPS"
      port: 443

  - id: "gateway-to-lb"
    source: "api-gateway"
    target: "load-balancer"
    type: "http"
    label: "Route Requests"

  - id: "lb-to-user-service"
    source: "load-balancer"
    target: "user-service"
    type: "http"
    label: "User Requests"

  - id: "lb-to-order-service"
    source: "load-balancer"
    target: "order-service"
    type: "http"
    label: "Order Requests"

  - id: "lb-to-payment-service"
    source: "load-balancer"
    target: "payment-service"
    type: "http"
    label: "Payment Requests"

  - id: "user-service-to-db"
    source: "user-service"
    target: "user-db"
    type: "database_connection"
    label: "User Data"

  - id: "order-service-to-db"
    source: "order-service"
    target: "order-db"
    type: "database_connection"
    label: "Order Data"

  - id: "payment-service-to-db"
    source: "payment-service"
    target: "payment-db"
    type: "database_connection"
    label: "Payment Data"

  - id: "services-to-queue"
    source: "order-service"
    target: "message-queue"
    type: "message_queue"
    label: "Events"
    bidirectional: true

  - id: "queue-to-notification"
    source: "message-queue"
    target: "notification-service"
    type: "message_queue"
    label: "Notifications"

  - id: "services-to-discovery"
    source: "user-service"
    target: "service-discovery"
    type: "api_call"
    label: "Register/Discover"
    bidirectional: true

# Customization options
customization:
  allowComponentAddition: true
  allowComponentRemoval: false
  allowConnectionModification: true
  requiredComponents:
    - "api-gateway"
    - "user-service"
  optionalComponents:
    - "notification-service"
    - "message-queue"
  configurationSchema:
    serviceCount:
      type: "number"
      min: 2
      max: 20
      default: 4
    databaseType:
      type: "string"
      enum: ["postgresql", "mysql", "mongodb"]
      default: "postgresql"
    messageQueue:
      type: "boolean"
      default: true
    monitoring:
      type: "boolean"
      default: false
