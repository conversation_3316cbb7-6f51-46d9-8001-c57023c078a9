<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 727.3125 362.7794189453125" style="max-width: 727.3125px;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-1752249559693"><style>#mermaid-1752249559693{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-1752249559693 .error-icon{fill:#a44141;}#mermaid-1752249559693 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-1752249559693 .edge-thickness-normal{stroke-width:2px;}#mermaid-1752249559693 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1752249559693 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1752249559693 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1752249559693 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1752249559693 .marker{fill:#333;stroke:#333;}#mermaid-1752249559693 .marker.cross{stroke:#333;}#mermaid-1752249559693 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-1752249559693 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-1752249559693 .cluster-label text{fill:#F9FFFE;}#mermaid-1752249559693 .cluster-label span,#mermaid-1752249559693 p{color:#F9FFFE;}#mermaid-1752249559693 .label text,#mermaid-1752249559693 span,#mermaid-1752249559693 p{fill:#ccc;color:#ccc;}#mermaid-1752249559693 .node rect,#mermaid-1752249559693 .node circle,#mermaid-1752249559693 .node ellipse,#mermaid-1752249559693 .node polygon,#mermaid-1752249559693 .node path{fill:#1f2020;stroke:#81B1DB;stroke-width:1px;}#mermaid-1752249559693 .flowchart-label text{text-anchor:middle;}#mermaid-1752249559693 .node .label{text-align:center;}#mermaid-1752249559693 .node.clickable{cursor:pointer;}#mermaid-1752249559693 .arrowheadPath{fill:lightgrey;}#mermaid-1752249559693 .edgePath .path{stroke:#333;stroke-width:2.0px;}#mermaid-1752249559693 .flowchart-link{stroke:#333;fill:none;}#mermaid-1752249559693 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-1752249559693 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-1752249559693 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-1752249559693 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-1752249559693 .cluster text{fill:#F9FFFE;}#mermaid-1752249559693 .cluster span,#mermaid-1752249559693 p{color:#F9FFFE;}#mermaid-1752249559693 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#ffe66d;border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-1752249559693 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-1752249559693 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-1752249559693_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-1752249559693_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-1752249559693_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-1752249559693_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-1752249559693_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-1752249559693_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-B" id="L-A-B-0" d="M282.82,26.545L253.027,31.954C223.233,37.363,163.646,48.182,133.852,56.874C104.059,65.567,104.059,72.133,104.059,75.417L104.059,78.7"></path><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-C" id="L-A-C-0" d="M335.391,34L335.391,38.167C335.391,42.333,335.391,50.667,335.391,58.117C335.391,65.567,335.391,72.133,335.391,75.417L335.391,78.7"></path><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-D" id="L-A-D-0" d="M387.961,24.414L428.832,30.179C469.703,35.943,551.445,47.471,592.316,56.519C633.188,65.567,633.188,72.133,633.188,75.417L633.188,78.7"></path><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-B LE-E" id="L-B-E-0" d="M76.204,118L69.377,122.167C62.55,126.333,48.896,134.667,42.069,142.645C35.242,150.623,35.242,158.246,35.242,162.057L35.242,165.869"></path><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-F" id="L-C-F-0" d="M331.343,118L330.351,122.167C329.359,126.333,327.375,134.667,326.383,142.117C325.391,149.567,325.391,156.133,325.391,159.417L325.391,162.7"></path><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-D LE-G" id="L-D-G-0" d="M577,116.578L561.117,120.982C545.234,125.386,513.469,134.193,496.558,142.303C479.648,150.414,477.593,157.828,476.565,161.535L475.538,165.242"></path><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-B LE-H" id="L-B-H-0" d="M131.913,118L138.74,122.167C145.567,126.333,159.221,134.667,166.048,144.898C172.875,155.13,172.875,167.26,172.875,173.325L172.875,179.39"></path><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-I" id="L-C-I-0" d="M366.3,118L373.875,122.167C381.451,126.333,396.602,134.667,429.54,145.555C462.477,156.444,513.201,169.888,538.563,176.61L563.924,183.332"></path><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-D LE-I" id="L-D-I-0" d="M639.831,118L641.46,122.167C643.088,126.333,646.345,134.667,646.268,144.931C646.191,155.195,642.78,167.39,641.075,173.488L639.369,179.586"></path><path marker-end="url(#mermaid-1752249559693_flowchart-pointEnd)" style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-H LE-J" id="L-H-J-0" d="M172.875,218.69L172.875,225.638C172.875,232.586,172.875,246.483,172.875,256.715C172.875,266.946,172.875,273.513,172.875,276.796L172.875,280.08"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(335.390625, 17)" id="flowchart-A-0" class="node default default flowchart-label"><rect height="34" width="105.140625" y="-17" x="-52.5703125" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-45.0703125, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="90.140625"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">API Gateway</span></div></foreignObject></g></g><g transform="translate(104.05859375, 101)" id="flowchart-B-1" class="node default default flowchart-label"><rect height="34" width="103.3125" y="-17" x="-51.65625" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-44.15625, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="88.3125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">User Service</span></div></foreignObject></g></g><g transform="translate(335.390625, 101)" id="flowchart-C-3" class="node default default flowchart-label"><rect height="34" width="133.078125" y="-17" x="-66.5390625" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-59.0390625, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="118.078125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Payment Service</span></div></foreignObject></g></g><g transform="translate(633.1875, 101)" id="flowchart-D-5" class="node default default flowchart-label"><rect height="34" width="112.375" y="-17" x="-56.1875" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-48.6875, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="97.375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Order Service</span></div></foreignObject></g></g><g transform="translate(35.2421875, 201.68978881835938)" id="flowchart-E-7" class="node default default flowchart-label"><path transform="translate(-35.2421875,-30.521101430740945)" d="M 0,9.014067620493964 a 35.2421875,9.014067620493964 0,0,0 70.484375 0 a 35.2421875,9.014067620493964 0,0,0 -70.484375 0 l 0,43.01406762049396 a 35.2421875,9.014067620493964 0,0,0 70.484375 0 l 0,-43.01406762049396" style=""></path><g transform="translate(-27.7421875, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="55.484375"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">User DB</span></div></foreignObject></g></g><g transform="translate(325.390625, 201.68978881835938)" id="flowchart-F-9" class="node default default flowchart-label"><path transform="translate(-50.125,-33.68978912319645)" d="M 0,11.126526082130965 a 50.125,11.126526082130965 0,0,0 100.25 0 a 50.125,11.126526082130965 0,0,0 -100.25 0 l 0,45.126526082130965 a 50.125,11.126526082130965 0,0,0 100.25 0 l 0,-45.126526082130965" style=""></path><g transform="translate(-42.625, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="85.25"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Payment DB</span></div></foreignObject></g></g><g transform="translate(465.2890625, 201.68978881835938)" id="flowchart-G-11" class="node default default flowchart-label"><path transform="translate(-39.7734375,-31.5834924757467)" d="M 0,9.722328317164465 a 39.7734375,9.722328317164465 0,0,0 79.546875 0 a 39.7734375,9.722328317164465 0,0,0 -79.546875 0 l 0,43.72232831716447 a 39.7734375,9.722328317164465 0,0,0 79.546875 0 l 0,-43.72232831716447" style=""></path><g transform="translate(-32.2734375, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="64.546875"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Order DB</span></div></foreignObject></g></g><g transform="translate(172.875, 201.68978881835938)" id="flowchart-H-13" class="node default default flowchart-label"><rect height="34" width="104.78125" y="-17" x="-52.390625" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-44.890625, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="89.78125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Auth Service</span></div></foreignObject></g></g><g transform="translate(633.1875, 201.68978881835938)" id="flowchart-I-15" class="node default default flowchart-label"><rect height="34" width="156.25" y="-17" x="-78.125" ry="0" rx="0" style="" class="basic label-container"></rect><g transform="translate(-70.625, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="141.25"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Notification Service</span></div></foreignObject></g></g><g transform="translate(172.875, 316.0794982910156)" id="flowchart-J-19" class="node default default flowchart-label"><path transform="translate(-35.9765625,-30.699920666402225)" d="M 0,9.133280444268149 a 35.9765625,9.133280444268149 0,0,0 71.953125 0 a 35.9765625,9.133280444268149 0,0,0 -71.953125 0 l 0,43.13328044426815 a 35.9765625,9.133280444268149 0,0,0 71.953125 0 l 0,-43.13328044426815" style=""></path><g transform="translate(-28.4765625, -9.5)" style="" class="label"><rect></rect><foreignObject height="19" width="56.953125"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel">Auth DB</span></div></foreignObject></g></g></g></g></g></svg>