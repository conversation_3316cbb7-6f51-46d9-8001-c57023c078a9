import React from 'react';

const ExamplesPage: React.FC = () => {
  const examples = [
    {
      title: 'E-commerce Platform',
      description: 'microservices architecture with API gateway, user service, order service, payment service, and notification service',
      diagram: `graph TD
    A[API Gateway] --> B[User Service]
    A --> C[Order Service]
    A --> D[Payment Service]
    B --> E[(User DB)]
    C --> F[(Order DB)]
    D --> G[(Payment DB)]
    C --> H[Notification Service]`
    },
    {
      title: 'Serverless Web App',
      description: 'serverless architecture with CloudFront, API Gateway, Lambda functions, and DynamoDB',
      diagram: `graph TD
    A[CloudFront] --> B[API Gateway]
    B --> C[Auth Function]
    B --> D[User Function]
    B --> E[Data Function]
    C --> F[(DynamoDB)]
    D --> F
    E --> F`
    },
    {
      title: 'Monolithic Application',
      description: 'traditional monolithic architecture with web server, application server, and database',
      diagram: `graph TD
    A[Load Balancer] --> B[Web Server]
    B --> C[Application Server]
    C --> D[(Database)]
    C --> E[Cache]`
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Example Diagrams</h1>
          <p className="text-gray-600 mb-8">
            Explore these example architecture diagrams to see what ArchitekAI can generate.
          </p>
          
          <div className="space-y-8">
            {examples.map((example, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{example.title}</h3>
                
                <div className="grid lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Input Description:</h4>
                    <div className="bg-gray-100 p-3 rounded-lg text-sm font-mono">
                      "{example.description}"
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Generated Mermaid Diagram:</h4>
                    <div className="bg-gray-100 p-3 rounded-lg">
                      <pre className="text-xs font-mono whitespace-pre-wrap">{example.diagram}</pre>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 flex gap-2">
                  <button
                    onClick={() => navigator.clipboard.writeText(example.description)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
                  >
                    Copy Description
                  </button>
                  <button
                    onClick={() => navigator.clipboard.writeText(example.diagram)}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-semibold transition-colors"
                  >
                    Copy Diagram
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExamplesPage;
