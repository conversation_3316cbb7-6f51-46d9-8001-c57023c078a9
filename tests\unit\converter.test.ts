/**
 * Tests for MermaidConverter
 */

import { MermaidConverter, ConversionOptions } from '../../src/core/converter';
import fs from 'fs-extra';
import path from 'path';
import os from 'os';

describe('MermaidConverter', () => {
  let converter: MermaidConverter;
  let tempDir: string;
  let testMermaidFile: string;

  const sampleMermaidContent = `graph TD
    A[API Gateway] --> B[User Service]
    A --> C[Payment Service]
    B --> D[(User DB)]
    C --> E[(Payment DB)]`;

  beforeAll(async () => {
    // Create temporary directory for test files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'architek-ai-test-'));
    testMermaidFile = path.join(tempDir, 'test-diagram.mmd');
    await fs.writeFile(testMermaidFile, sampleMermaidContent);
  });

  afterAll(async () => {
    // Clean up temporary directory
    await fs.remove(tempDir);
  });

  beforeEach(() => {
    converter = new MermaidConverter();
  });

  afterEach(async () => {
    if (converter) {
      await converter.cleanup();
    }
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      await expect(converter.initialize()).resolves.not.toThrow();
    });

    it('should handle multiple initialization calls', async () => {
      await converter.initialize();
      await expect(converter.initialize()).resolves.not.toThrow();
    });

    it('should cleanup successfully', async () => {
      await converter.initialize();
      await expect(converter.cleanup()).resolves.not.toThrow();
    });
  });

  describe('single file conversion', () => {
    beforeEach(async () => {
      await converter.initialize();
    });

    it('should convert mermaid file to JPG', async () => {
      const options: ConversionOptions = {
        format: 'jpg',
        quality: 90,
        width: 800,
        height: 600,
        overwrite: true,
      };

      const result = await converter.convertFile(testMermaidFile, options);

      expect(result.success).toBe(true);
      expect(result.format).toBe('jpg');
      expect(result.inputFile).toBe(testMermaidFile);
      expect(result.outputFile).toMatch(/\.jpg$/);
      expect(result.size).toBeGreaterThan(0);
      expect(result.width).toBeGreaterThan(0);
      expect(result.height).toBeGreaterThan(0);
      expect(result.duration).toBeGreaterThan(0);

      // Verify output file exists
      expect(await fs.pathExists(result.outputFile)).toBe(true);

      // Clean up
      await fs.remove(result.outputFile);
    }, 30000);

    it('should convert mermaid file to PNG', async () => {
      const options: ConversionOptions = {
        format: 'png',
        width: 1024,
        height: 768,
        overwrite: true,
      };

      const result = await converter.convertFile(testMermaidFile, options);

      expect(result.success).toBe(true);
      expect(result.format).toBe('png');
      expect(result.outputFile).toMatch(/\.png$/);

      // Clean up
      await fs.remove(result.outputFile);
    }, 30000);

    it('should convert mermaid file to SVG', async () => {
      const options: ConversionOptions = {
        format: 'svg',
        theme: 'dark',
        overwrite: true,
      };

      const result = await converter.convertFile(testMermaidFile, options);

      expect(result.success).toBe(true);
      expect(result.format).toBe('svg');
      expect(result.outputFile).toMatch(/\.svg$/);

      // Clean up
      await fs.remove(result.outputFile);
    }, 30000);

    it('should handle different themes', async () => {
      const themes: Array<'default' | 'dark' | 'forest' | 'neutral'> = ['default', 'dark', 'forest', 'neutral'];

      for (const theme of themes) {
        const options: ConversionOptions = {
          format: 'svg',
          theme,
          overwrite: true,
        };

        const result = await converter.convertFile(testMermaidFile, options);
        expect(result.success).toBe(true);
        
        // Clean up
        await fs.remove(result.outputFile);
      }
    }, 60000);

    it('should handle custom output directory', async () => {
      const customOutputDir = path.join(tempDir, 'custom-output');
      await fs.ensureDir(customOutputDir);

      const options: ConversionOptions = {
        format: 'jpg',
        outputDir: customOutputDir,
        overwrite: true,
      };

      const result = await converter.convertFile(testMermaidFile, options);

      expect(result.success).toBe(true);
      expect(result.outputFile).toContain(customOutputDir);
      expect(await fs.pathExists(result.outputFile)).toBe(true);

      // Clean up
      await fs.remove(result.outputFile);
    }, 30000);

    it('should handle quality settings for JPEG', async () => {
      const qualities = [10, 50, 90, 100];

      for (const quality of qualities) {
        const options: ConversionOptions = {
          format: 'jpg',
          quality,
          overwrite: true,
        };

        const result = await converter.convertFile(testMermaidFile, options);
        expect(result.success).toBe(true);
        
        // Clean up
        await fs.remove(result.outputFile);
      }
    }, 60000);
  });

  describe('error handling', () => {
    beforeEach(async () => {
      await converter.initialize();
    });

    it('should handle non-existent input file', async () => {
      const nonExistentFile = path.join(tempDir, 'non-existent.mmd');
      const options: ConversionOptions = {
        format: 'jpg',
        overwrite: true,
      };

      const result = await converter.convertFile(nonExistentFile, options);

      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });

    it('should handle empty input file', async () => {
      const emptyFile = path.join(tempDir, 'empty.mmd');
      await fs.writeFile(emptyFile, '');

      const options: ConversionOptions = {
        format: 'jpg',
        overwrite: true,
      };

      const result = await converter.convertFile(emptyFile, options);

      expect(result.success).toBe(false);
      expect(result.error).toContain('empty');

      // Clean up
      await fs.remove(emptyFile);
    });

    it('should handle invalid mermaid syntax', async () => {
      const invalidFile = path.join(tempDir, 'invalid.mmd');
      await fs.writeFile(invalidFile, 'invalid mermaid syntax here');

      const options: ConversionOptions = {
        format: 'jpg',
        overwrite: true,
      };

      const result = await converter.convertFile(invalidFile, options);

      // The result might succeed or fail depending on how Mermaid handles invalid syntax
      // We just ensure it doesn't crash
      expect(typeof result.success).toBe('boolean');

      // Clean up
      await fs.remove(invalidFile);
      if (result.success && result.outputFile) {
        await fs.remove(result.outputFile);
      }
    }, 30000);

    it('should handle existing output file without overwrite', async () => {
      const options: ConversionOptions = {
        format: 'jpg',
        overwrite: false,
      };

      // First conversion
      const result1 = await converter.convertFile(testMermaidFile, options);
      expect(result1.success).toBe(true);

      // Second conversion without overwrite
      const result2 = await converter.convertFile(testMermaidFile, options);
      expect(result2.success).toBe(false);
      expect(result2.error).toContain('already exists');

      // Clean up
      await fs.remove(result1.outputFile);
    }, 30000);
  });

  describe('batch conversion', () => {
    let testFiles: string[];

    beforeAll(async () => {
      // Create multiple test files
      testFiles = [];
      for (let i = 0; i < 3; i++) {
        const filePath = path.join(tempDir, `test-${i}.mmd`);
        await fs.writeFile(filePath, `graph TD\n    A${i}[Component ${i}] --> B${i}[Service ${i}]`);
        testFiles.push(filePath);
      }
    });

    beforeEach(async () => {
      await converter.initialize();
    });

    it('should convert multiple files successfully', async () => {
      const options: ConversionOptions = {
        format: 'jpg',
        overwrite: true,
      };

      const result = await converter.convertBatch(testFiles, options);

      expect(result.totalProcessed).toBe(3);
      expect(result.successful).toBe(3);
      expect(result.failed).toBe(0);
      expect(result.results).toHaveLength(3);
      expect(result.errors).toHaveLength(0);

      // Verify all output files exist
      for (const fileResult of result.results) {
        expect(fileResult.success).toBe(true);
        expect(await fs.pathExists(fileResult.outputFile)).toBe(true);
        
        // Clean up
        await fs.remove(fileResult.outputFile);
      }
    }, 60000);

    it('should handle mixed success and failure in batch', async () => {
      // Add a non-existent file to the batch
      const mixedFiles = [...testFiles, path.join(tempDir, 'non-existent.mmd')];
      
      const options: ConversionOptions = {
        format: 'png',
        overwrite: true,
      };

      const result = await converter.convertBatch(mixedFiles, options);

      expect(result.totalProcessed).toBe(4);
      expect(result.successful).toBe(3);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);

      // Clean up successful conversions
      for (const fileResult of result.results) {
        if (fileResult.success && await fs.pathExists(fileResult.outputFile)) {
          await fs.remove(fileResult.outputFile);
        }
      }
    }, 60000);
  });
});
