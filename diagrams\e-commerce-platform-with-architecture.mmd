graph TD
    external[External Clients]
    api_gateway{{API Gateway}}
    service_discovery[Service Discovery]
    load_balancer[/Load Balancer\]
    user_service[User Service]
    order_service[Order Service]
    payment_service[Payment Service]
    notification_service[Notification Service]
    user_db[(User Database)]
    order_db[(Order Database)]
    payment_db[(Payment Database)]
    message_queue>Message Queue]
    comp_1[Service]
    comp_2[Microservice]

    external ==>|Client Requests| api_gateway
    api_gateway -->|Route Requests| load_balancer
    load_balancer -->|User Requests| user_service
    load_balancer -->|Order Requests| order_service
    load_balancer -->|Payment Requests| payment_service
    user_service -.->|User Data| user_db
    order_service -.->|Order Data| order_db
    payment_service -.->|Payment Data| payment_db
    order_service -.->|Events| message_queue
    message_queue -.->|Notifications| notification_service
    user_service -->|Register/Discover| service_discovery
