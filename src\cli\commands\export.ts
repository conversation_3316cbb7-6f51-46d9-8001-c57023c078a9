/**
 * Export Command for ArchitekAI CLI
 * 
 * Handles exporting existing diagrams to different formats.
 */

import { Command } from 'commander';
import chalk from 'chalk';
import path from 'path';
import { glob } from 'glob';
import { ConfigManager } from '@/core/config/manager';
import { OutputFormat } from '@/types/architecture';
import { createLogger } from '@/utils/logger';
import { 
  createSpinner,
  createProgressBar,
  displayError,
  displayInfo,
  displayWarning,
  ensureOutputPath,
  formatDuration,
  formatFileSize,
  confirmAction,
} from '../utils';

const logger = createLogger('ExportCommand');

interface ExportOptions {
  input?: string;
  output?: string;
  format: OutputFormat;
  quality?: 'low' | 'medium' | 'high';
  theme?: string;
  width?: number;
  height?: number;
  scale?: number;
  background?: string;
  transparent?: boolean;
  overwrite?: boolean;
  verbose?: boolean;
  batch?: boolean;
}

export function exportCommand(configManager: ConfigManager): Command {
  const command = new Command('export');

  command
    .description('Export diagrams to different formats')
    .option('-i, --input <path>', 'input diagram file or pattern')
    .option('-o, --output <path>', 'output file or directory')
    .requiredOption('-f, --format <format>', 'export format (svg, png, pdf, json, yaml)')
    .option('-q, --quality <level>', 'export quality (low, medium, high)', 'medium')
    .option('-t, --theme <name>', 'theme for export (light, dark, custom)')
    .option('-w, --width <pixels>', 'output width in pixels')
    .option('-h, --height <pixels>', 'output height in pixels')
    .option('-s, --scale <factor>', 'scale factor (0.5, 1.0, 2.0, etc.)', '1.0')
    .option('-b, --background <color>', 'background color (hex, rgb, or name)')
    .option('--transparent', 'transparent background (PNG/SVG only)')
    .option('--overwrite', 'overwrite existing files without confirmation')
    .option('--verbose', 'enable verbose output')
    .option('--batch', 'batch export multiple files')
    .addHelpText('after', `
Examples:
  ${chalk.cyan('architekAI export -i diagram.mmd -f svg -o diagram.svg')}
  ${chalk.cyan('architekAI export -i "*.mmd" -f png --batch -o ./exports/')}
  ${chalk.cyan('architekAI export -i diagram.puml -f pdf --quality high --theme dark')}
  ${chalk.cyan('architekAI export -i diagram.mmd -f svg --width 1920 --height 1080')}
    `)
    .action(async (options: ExportOptions) => {
      await executeExport(options, configManager);
    });

  return command;
}

async function executeExport(options: ExportOptions, configManager: ConfigManager): Promise<void> {
  const startTime = Date.now();
  let spinner = createSpinner('Initializing export...');
  
  try {
    spinner.start();

    // Validate options
    validateExportOptions(options);

    // Get configuration
    const config = configManager.getConfig();
    
    // Merge options with config defaults
    const mergedOptions = {
      quality: options.quality || 'medium',
      scale: parseFloat(String(options.scale || 1.0)),
      verbose: options.verbose ?? config.cli.verbose,
      ...options,
    };

    // Find input files
    spinner.text = 'Finding input files...';
    const inputFiles = await findInputFiles(mergedOptions);

    if (inputFiles.length === 0) {
      spinner.fail('No input files found');
      displayError(`No files found matching: ${mergedOptions.input || 'current directory'}`);
      process.exit(1);
    }

    spinner.stop();
    displayInfo(`Found ${inputFiles.length} file(s) to export`);

    if (mergedOptions.verbose) {
      console.log(chalk.gray('Input files:'));
      inputFiles.forEach(file => {
        console.log(chalk.gray(`  • ${file.path} (${file.format})`));
      });
    }

    // Confirm batch export
    if (inputFiles.length > 1 && !mergedOptions.batch) {
      const shouldBatch = await confirmAction(
        `Export ${inputFiles.length} files in batch mode?`, 
        true
      );
      
      if (!shouldBatch) {
        displayInfo('Export cancelled. Use --batch flag for multiple files.');
        return;
      }
      
      mergedOptions.batch = true;
    }

    // Process exports
    spinner = createSpinner('Exporting diagrams...');
    spinner.start();

    const results = await processExports(inputFiles, mergedOptions);

    const endTime = Date.now();
    const totalDuration = endTime - startTime;

    spinner.succeed('Export completed!');

    // Display results
    displayExportResults(results, totalDuration);

  } catch (error) {
    if (spinner) {
      spinner.fail('Export failed');
    }
    
    logger.error('Export command failed:', error);
    displayError(error instanceof Error ? error.message : 'Unknown error occurred');
    process.exit(1);
  }
}

function validateExportOptions(options: ExportOptions): void {
  // Validate format
  const supportedFormats = ['svg', 'png', 'pdf', 'json', 'yaml'];
  if (!supportedFormats.includes(options.format)) {
    throw new Error(`Unsupported export format: ${options.format}. Supported: ${supportedFormats.join(', ')}`);
  }

  // Validate quality
  if (options.quality && !['low', 'medium', 'high'].includes(options.quality)) {
    throw new Error(`Invalid quality level: ${options.quality}. Use: low, medium, high`);
  }

  // Validate scale
  if (options.scale) {
    const scale = parseFloat(String(options.scale));
    if (isNaN(scale) || scale <= 0 || scale > 10) {
      throw new Error(`Invalid scale factor: ${options.scale}. Use a number between 0.1 and 10`);
    }
  }

  // Validate dimensions
  if (options.width && (isNaN(Number(options.width)) || Number(options.width) <= 0)) {
    throw new Error(`Invalid width: ${options.width}. Use a positive number`);
  }
  
  if (options.height && (isNaN(Number(options.height)) || Number(options.height) <= 0)) {
    throw new Error(`Invalid height: ${options.height}. Use a positive number`);
  }

  // Validate transparent option for supported formats
  if (options.transparent && !['svg', 'png'].includes(options.format)) {
    displayWarning(`Transparent background not supported for ${options.format} format`);
  }
}

async function findInputFiles(options: ExportOptions): Promise<InputFile[]> {
  const fs = await import('fs-extra');
  
  if (!options.input) {
    // Look for diagram files in current directory
    const patterns = ['*.mmd', '*.puml', '*.drawio', '*.json'];
    const files: InputFile[] = [];
    
    for (const pattern of patterns) {
      const matches = await glob(pattern);
      for (const match of matches) {
        const format = detectInputFormat(match);
        if (format) {
          files.push({ path: match, format });
        }
      }
    }
    
    return files;
  }

  // Check if input is a single file or pattern
  const inputPath = options.input;
  
  if (await fs.pathExists(inputPath) && (await fs.stat(inputPath)).isFile()) {
    // Single file
    const format = detectInputFormat(inputPath);
    if (!format) {
      throw new Error(`Unsupported input file format: ${inputPath}`);
    }
    
    return [{ path: inputPath, format }];
  }

  // Pattern matching
  const matches = await glob(inputPath);
  const files: InputFile[] = [];
  
  for (const match of matches) {
    const format = detectInputFormat(match);
    if (format) {
      files.push({ path: match, format });
    }
  }
  
  return files;
}

function detectInputFormat(filePath: string): string | null {
  const ext = path.extname(filePath).toLowerCase();
  
  const formatMap: Record<string, string> = {
    '.mmd': 'mermaid',
    '.mermaid': 'mermaid',
    '.puml': 'plantuml',
    '.plantuml': 'plantuml',
    '.drawio': 'drawio',
    '.json': 'json',
    '.yaml': 'yaml',
    '.yml': 'yaml',
  };
  
  return formatMap[ext] || null;
}

async function processExports(inputFiles: InputFile[], options: ExportOptions): Promise<ExportResults> {
  const results: ExportResults = {
    processed: 0,
    successful: 0,
    failed: 0,
    errors: [],
    outputs: [],
    totalSize: 0,
  };

  const progressBar = createProgressBar(inputFiles.length);
  
  for (const inputFile of inputFiles) {
    try {
      progressBar.update(results.processed, `Exporting ${path.basename(inputFile.path)}`);
      
      const output = await exportFile(inputFile, options);
      
      results.successful++;
      results.outputs.push(output);
      results.totalSize += output.size;
      
    } catch (error) {
      results.failed++;
      results.errors.push({
        file: inputFile.path,
        error: error instanceof Error ? error.message : String(error),
      });
    }
    
    results.processed++;
  }

  progressBar.complete(`Exported ${results.successful} file(s)`);
  
  return results;
}

async function exportFile(inputFile: InputFile, options: ExportOptions): Promise<ExportOutput> {
  const fs = await import('fs-extra');
  
  // Read input file
  const inputContent = await fs.readFile(inputFile.path, 'utf8');
  
  // Generate output path
  const outputPath = generateOutputPath(inputFile.path, options);
  await ensureOutputPath(outputPath);
  
  // Check for existing file
  if (!options.overwrite && await fs.pathExists(outputPath)) {
    const shouldOverwrite = await confirmAction(`File ${outputPath} exists. Overwrite?`, false);
    if (!shouldOverwrite) {
      throw new Error('Export cancelled - file exists');
    }
  }

  // Convert diagram
  const exportedContent = await convertDiagram(inputContent, inputFile.format, options);
  
  // Save exported content
  if (typeof exportedContent === 'string') {
    await fs.writeFile(outputPath, exportedContent, 'utf8');
  } else {
    await fs.writeFile(outputPath, exportedContent);
  }
  
  const stats = await fs.stat(outputPath);
  
  return {
    inputFile: inputFile.path,
    outputFile: outputPath,
    size: stats.size,
    format: options.format,
  };
}

async function convertDiagram(
  content: string, 
  inputFormat: string, 
  options: ExportOptions
): Promise<string | Buffer> {
  // Mock implementation - replace with actual conversion logic
  displayInfo(`Converting ${inputFormat} to ${options.format}...`);
  
  switch (options.format) {
    case 'svg':
      return generateMockSVG(content, options);
    case 'png':
      return generateMockPNG(content, options);
    case 'pdf':
      return generateMockPDF(content, options);
    case 'json':
      return generateMockJSON(content, inputFormat);
    case 'yaml':
      return generateMockYAML(content, inputFormat);
    default:
      throw new Error(`Unsupported export format: ${options.format}`);
  }
}

function generateOutputPath(inputPath: string, options: ExportOptions): string {
  const inputDir = path.dirname(inputPath);
  const inputName = path.basename(inputPath, path.extname(inputPath));
  const extension = getExportExtension(options.format);
  
  if (options.output) {
    if (options.batch || path.extname(options.output) === '') {
      // Output is a directory
      return path.join(options.output, `${inputName}.${extension}`);
    } else {
      // Output is a specific file
      return options.output;
    }
  }
  
  return path.join(inputDir, `${inputName}.${extension}`);
}

function getExportExtension(format: string): string {
  const extensions: Record<string, string> = {
    svg: 'svg',
    png: 'png',
    pdf: 'pdf',
    json: 'json',
    yaml: 'yaml',
  };
  return extensions[format] || format;
}

// Mock conversion functions - replace with actual implementations
function generateMockSVG(content: string, options: ExportOptions): string {
  const width = options.width || 800;
  const height = options.height || 600;
  const background = options.transparent ? 'none' : (options.background || '#ffffff');
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="${background}"/>
  <text x="50%" y="50%" text-anchor="middle" font-family="Arial" font-size="16">
    Exported Diagram (Mock)
  </text>
  <!-- Original content: ${content.substring(0, 100)}... -->
</svg>`;
}

function generateMockPNG(_content: string, _options: ExportOptions): Buffer {
  // Mock PNG data - in real implementation, use a library like puppeteer or canvas
  const mockPngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    // ... rest would be actual PNG data
  ]);
  
  return mockPngData;
}

function generateMockPDF(_content: string, _options: ExportOptions): Buffer {
  // Mock PDF data - in real implementation, use a library like puppeteer or pdfkit
  const mockPdfData = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n>>\nendobj\n');
  
  return mockPdfData;
}

function generateMockJSON(content: string, inputFormat: string): string {
  return JSON.stringify({
    originalFormat: inputFormat,
    content: content,
    exportedAt: new Date().toISOString(),
    metadata: {
      version: '1.0.0',
      generator: 'ArchitekAI',
    },
  }, null, 2);
}

function generateMockYAML(content: string, inputFormat: string): string {
  const yaml = require('yaml');
  
  return yaml.stringify({
    originalFormat: inputFormat,
    content: content,
    exportedAt: new Date().toISOString(),
    metadata: {
      version: '1.0.0',
      generator: 'ArchitekAI',
    },
  });
}

function displayExportResults(results: ExportResults, totalDuration: number): void {
  console.log(chalk.cyan('\n📤 Export Results:'));
  console.log(chalk.gray('─'.repeat(30)));
  
  console.log(`${chalk.bold('Total Processed:')} ${results.processed}`);
  console.log(`${chalk.green('✅ Successful:')} ${results.successful}`);
  console.log(`${chalk.red('❌ Failed:')} ${results.failed}`);
  console.log(`${chalk.bold('Total Output Size:')} ${formatFileSize(results.totalSize)}`);
  console.log(`${chalk.bold('Duration:')} ${formatDuration(totalDuration)}`);
  
  if (results.errors.length > 0) {
    console.log(chalk.red('\n❌ Errors:'));
    results.errors.forEach(error => {
      console.log(chalk.red(`  • ${path.basename(error.file)}: ${error.error}`));
    });
  }
  
  if (results.successful > 0) {
    console.log(chalk.green(`\n✅ Successfully exported ${results.successful} diagram(s)`));
    
    if (results.outputs.length <= 5) {
      console.log(chalk.gray('\nOutput files:'));
      results.outputs.forEach(output => {
        console.log(chalk.gray(`  • ${output.outputFile} (${formatFileSize(output.size)})`));
      });
    }
  }
}

// Type definitions
interface InputFile {
  path: string;
  format: string;
}

interface ExportResults {
  processed: number;
  successful: number;
  failed: number;
  errors: Array<{ file: string; error: string }>;
  outputs: ExportOutput[];
  totalSize: number;
}

interface ExportOutput {
  inputFile: string;
  outputFile: string;
  size: number;
  format: string;
}
