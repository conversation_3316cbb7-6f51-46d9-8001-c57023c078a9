{"name": "architek-ai", "version": "1.0.0", "description": "Intelligent Architecture Diagram Generator - Transform natural language descriptions into professional architecture diagrams", "main": "dist/cli/index.js", "bin": {"architek-ai": "./dist/cli/index.js", "architekAI": "dist/cli/index.js"}, "scripts": {"build": "tsc && tsc-alias && npm run build:web", "build:cli": "tsc && tsc-alias", "build:web": "cd src/web/frontend && npm run build", "dev": "ts-node src/cli/index.ts", "dev:web": "concurrently \"npm run dev:api\" \"npm run dev:frontend\"", "dev:api": "ts-node-dev src/web/api/server.ts", "dev:frontend": "cd src/web/frontend && npm run dev", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build", "start": "node dist/cli/index.js", "start:web": "node dist/web/api/server.js"}, "keywords": ["architecture", "diagram", "mermaid", "plantuml", "cli", "nlp", "microservices", "system-design", "documentation", "typescript"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/ArchitekAI.git"}, "bugs": {"url": "https://github.com/HectorTa1989/ArchitekAI/issues"}, "homepage": "https://github.com/HectorTa1989/ArchitekAI#readme", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"@types/uuid": "^10.0.0", "boxen": "^5.1.2", "chalk": "^4.1.2", "commander": "^11.1.0", "compromise": "^14.10.0", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "figlet": "^1.7.0", "fs-extra": "^11.1.1", "glob": "^10.3.10", "helmet": "^7.1.0", "inquirer": "^9.2.12", "joi": "^17.11.0", "lodash": "^4.17.21", "morgan": "^1.10.0", "natural": "^6.12.0", "ora": "^5.4.1", "winston": "^3.11.0", "yaml": "^2.3.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/figlet": "^1.5.8", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/node": "^20.8.10", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.1.0", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.16", "typescript": "^5.2.2"}, "files": ["dist/", "templates/", "config/", "README.md", "LICENSE"], "publishConfig": {"access": "public"}}