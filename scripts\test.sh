#!/bin/bash

# Architek-AI Test Script
# Comprehensive testing script for all components

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test configuration
TEST_DIR="./test-output"
TEMP_DIR="./temp-test"

# Cleanup function
cleanup() {
    log_info "Cleaning up test files..."
    if [ -d "$TEST_DIR" ]; then
        rm -rf "$TEST_DIR"
    fi
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
}

# Setup test environment
setup_test_env() {
    log_info "Setting up test environment..."
    
    # Create test directories
    mkdir -p "$TEST_DIR"
    mkdir -p "$TEMP_DIR"
    
    # Create test description files
    cat > "$TEMP_DIR/microservices.txt" << 'EOF'
microservices architecture with API gateway, user service, order service, payment service, and PostgreSQL databases
EOF

    cat > "$TEMP_DIR/serverless.txt" << 'EOF'
serverless architecture with API gateway, lambda functions, DynamoDB database, and S3 storage
EOF

    cat > "$TEMP_DIR/monolithic.txt" << 'EOF'
monolithic web application with load balancer, application server, and MySQL database
EOF

    log_success "Test environment setup completed"
}

# Test CLI build
test_cli_build() {
    log_info "Testing CLI build..."
    
    if npm run build:cli; then
        log_success "CLI build successful"
    else
        log_error "CLI build failed"
        return 1
    fi
    
    # Check if CLI entry point exists
    if [ -f "dist/cli/index.js" ]; then
        log_success "CLI entry point exists"
    else
        log_error "CLI entry point not found"
        return 1
    fi
}

# Test CLI functionality
test_cli_functionality() {
    log_info "Testing CLI functionality..."
    
    # Test version command
    if node dist/cli/index.js --version; then
        log_success "Version command works"
    else
        log_error "Version command failed"
        return 1
    fi
    
    # Test help command
    if node dist/cli/index.js --help > /dev/null; then
        log_success "Help command works"
    else
        log_error "Help command failed"
        return 1
    fi
    
    # Test generate command
    log_info "Testing generate command..."
    if node dist/cli/index.js generate "simple microservices with API gateway and user service" \
        --format mermaid \
        --output "$TEST_DIR/test-diagram.mmd"; then
        log_success "Generate command works"
        
        # Check if output file was created
        if [ -f "$TEST_DIR/test-diagram.mmd" ]; then
            log_success "Output file created successfully"
        else
            log_warning "Output file not found"
        fi
    else
        log_error "Generate command failed"
        return 1
    fi
}

# Test template functionality
test_templates() {
    log_info "Testing template functionality..."
    
    # Test template list
    if node dist/cli/index.js template list > /dev/null; then
        log_success "Template list command works"
    else
        log_error "Template list command failed"
        return 1
    fi
    
    # Test template usage
    if node dist/cli/index.js template use microservices \
        --output "$TEST_DIR/template-test.mmd"; then
        log_success "Template use command works"
    else
        log_warning "Template use command failed (templates may not be loaded)"
    fi
}

# Test batch processing
test_batch_processing() {
    log_info "Testing batch processing..."
    
    if node dist/cli/index.js batch \
        --input "$TEMP_DIR/*.txt" \
        --output "$TEST_DIR" \
        --format mermaid; then
        log_success "Batch processing works"
        
        # Check if output files were created
        local file_count=$(find "$TEST_DIR" -name "*.mmd" | wc -l)
        if [ "$file_count" -gt 0 ]; then
            log_success "Batch output files created: $file_count"
        else
            log_warning "No batch output files found"
        fi
    else
        log_warning "Batch processing failed (may require additional setup)"
    fi
}

# Test web API build
test_web_build() {
    log_info "Testing web API build..."
    
    # Check if web server entry point exists
    if [ -f "dist/web/api/server.js" ]; then
        log_success "Web API entry point exists"
    else
        log_warning "Web API entry point not found"
        return 1
    fi
}

# Test web API functionality
test_web_api() {
    log_info "Testing web API functionality..."
    
    # Start web server in background
    log_info "Starting web server..."
    node dist/web/api/server.js &
    SERVER_PID=$!
    
    # Wait for server to start
    sleep 5
    
    # Test health endpoint
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        log_success "Health endpoint works"
    else
        log_error "Health endpoint failed"
        kill $SERVER_PID 2>/dev/null || true
        return 1
    fi
    
    # Test API documentation endpoint
    if curl -f http://localhost:3000/api/docs > /dev/null 2>&1; then
        log_success "API docs endpoint works"
    else
        log_warning "API docs endpoint failed"
    fi
    
    # Test generate endpoint
    log_info "Testing generate API endpoint..."
    local response=$(curl -s -X POST http://localhost:3000/api/generate \
        -H "Content-Type: application/json" \
        -d '{"description": "simple microservices with API gateway", "format": "mermaid"}' \
        -w "%{http_code}")
    
    if [[ "$response" == *"200" ]]; then
        log_success "Generate API endpoint works"
    else
        log_warning "Generate API endpoint failed"
    fi
    
    # Stop server
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
}

# Test frontend build
test_frontend_build() {
    log_info "Testing frontend build..."
    
    if [ -d "src/web/frontend" ]; then
        cd src/web/frontend
        
        if npm run build; then
            log_success "Frontend build successful"
            
            # Check if build output exists
            if [ -d "dist" ]; then
                log_success "Frontend build output exists"
            else
                log_warning "Frontend build output not found"
            fi
        else
            log_error "Frontend build failed"
            cd ../../..
            return 1
        fi
        
        cd ../../..
    else
        log_warning "Frontend directory not found"
    fi
}

# Test configuration
test_configuration() {
    log_info "Testing configuration management..."
    
    # Create test config
    cat > "$TEMP_DIR/.architek-ai.yaml" << 'EOF'
output:
  defaultFormat: "plantuml"
  directory: "./test-diagrams"
logging:
  level: "debug"
EOF

    # Test with custom config
    cd "$TEMP_DIR"
    if node ../dist/cli/index.js generate "test architecture" \
        --output "config-test.puml"; then
        log_success "Configuration loading works"
    else
        log_warning "Configuration test failed"
    fi
    cd ..
}

# Test Docker build
test_docker_build() {
    log_info "Testing Docker build..."
    
    if command -v docker &> /dev/null; then
        if docker build -t architek-ai-test .; then
            log_success "Docker build successful"
            
            # Test Docker run
            if docker run --rm architek-ai-test --version; then
                log_success "Docker run test successful"
            else
                log_warning "Docker run test failed"
            fi
            
            # Cleanup Docker image
            docker rmi architek-ai-test 2>/dev/null || true
        else
            log_error "Docker build failed"
            return 1
        fi
    else
        log_warning "Docker not available, skipping Docker tests"
    fi
}

# Run unit tests
test_unit_tests() {
    log_info "Running unit tests..."
    
    if npm run test:unit; then
        log_success "Unit tests passed"
    else
        log_error "Unit tests failed"
        return 1
    fi
}

# Test linting
test_linting() {
    log_info "Testing code linting..."
    
    if npm run lint; then
        log_success "Linting passed"
    else
        log_error "Linting failed"
        return 1
    fi
}

# Main test execution
main() {
    log_info "Starting Architek-AI comprehensive test suite..."
    
    # Setup
    setup_test_env
    
    # Track test results
    local tests_passed=0
    local tests_failed=0
    local tests_warned=0
    
    # Run tests
    local test_functions=(
        "test_linting"
        "test_unit_tests"
        "test_cli_build"
        "test_cli_functionality"
        "test_templates"
        "test_batch_processing"
        "test_configuration"
        "test_web_build"
        "test_web_api"
        "test_frontend_build"
        "test_docker_build"
    )
    
    for test_func in "${test_functions[@]}"; do
        log_info "Running $test_func..."
        
        if $test_func; then
            ((tests_passed++))
        else
            ((tests_failed++))
        fi
        
        echo ""
    done
    
    # Cleanup
    cleanup
    
    # Summary
    echo ""
    log_info "Test Summary:"
    echo "=============="
    log_success "Tests Passed: $tests_passed"
    if [ $tests_failed -gt 0 ]; then
        log_error "Tests Failed: $tests_failed"
    fi
    if [ $tests_warned -gt 0 ]; then
        log_warning "Tests with Warnings: $tests_warned"
    fi
    
    echo ""
    if [ $tests_failed -eq 0 ]; then
        log_success "All critical tests passed! Architek-AI is ready for use."
        exit 0
    else
        log_error "Some tests failed. Please review the output above."
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Architek-AI Test Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --quick        Run only quick tests"
        echo "  --full         Run all tests including Docker"
        echo ""
        echo "Examples:"
        echo "  $0             Run standard test suite"
        echo "  $0 --quick     Run quick tests only"
        echo "  $0 --full      Run comprehensive test suite"
        exit 0
        ;;
    --quick)
        log_info "Running quick test suite..."
        setup_test_env
        test_linting && test_unit_tests && test_cli_build && test_cli_functionality
        cleanup
        ;;
    --full)
        log_info "Running full test suite..."
        main
        ;;
    *)
        main
        ;;
esac
