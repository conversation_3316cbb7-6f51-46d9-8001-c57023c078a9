import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';

interface MermaidPreviewProps {
  content: string;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  className?: string;
  onError?: (error: string) => void;
  onRender?: (svg: string) => void;
}

const MermaidPreview: React.FC<MermaidPreviewProps> = ({
  content,
  theme = 'default',
  className = '',
  onError,
  onRender,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize Mermaid
    mermaid.initialize({
      startOnLoad: false,
      theme,
      themeVariables: {
        primaryColor: '#ff6b6b',
        primaryTextColor: '#333',
        primaryBorderColor: '#ff6b6b',
        lineColor: '#333',
        secondaryColor: '#4ecdc4',
        tertiaryColor: '#ffe66d',
      },
      securityLevel: 'loose',
    });
  }, [theme]);

  useEffect(() => {
    if (!content.trim() || !containerRef.current) {
      return;
    }

    const renderDiagram = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Clear previous content
        if (containerRef.current) {
          containerRef.current.innerHTML = '';
        }

        // Generate unique ID for this diagram
        const diagramId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // Render the diagram
        const { svg } = await mermaid.render(diagramId, content);

        if (containerRef.current) {
          containerRef.current.innerHTML = svg;
          
          // Call onRender callback with the SVG content
          if (onRender) {
            onRender(svg);
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to render diagram';
        setError(errorMessage);
        
        if (onError) {
          onError(errorMessage);
        }
        
        if (containerRef.current) {
          containerRef.current.innerHTML = `
            <div class="flex items-center justify-center h-32 bg-red-50 border border-red-200 rounded-lg">
              <div class="text-center">
                <div class="text-red-600 text-sm font-medium">Diagram Error</div>
                <div class="text-red-500 text-xs mt-1">${errorMessage}</div>
              </div>
            </div>
          `;
        }
      } finally {
        setIsLoading(false);
      }
    };

    renderDiagram();
  }, [content, theme, onError, onRender]);

  return (
    <div className={`mermaid-preview ${className}`}>
      {isLoading && (
        <div className="flex items-center justify-center h-32 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <div className="text-gray-600 text-sm mt-2">Rendering diagram...</div>
          </div>
        </div>
      )}
      
      <div
        ref={containerRef}
        className={`mermaid-container ${isLoading ? 'hidden' : ''}`}
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '200px',
        }}
      />
      
      {error && !isLoading && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-red-800 text-sm font-medium">Rendering Error</div>
          <div className="text-red-600 text-xs mt-1">{error}</div>
        </div>
      )}
    </div>
  );
};

export default MermaidPreview;
