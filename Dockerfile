# Multi-stage build for Architek-AI
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/
COPY templates/ ./templates/
COPY config/ ./config/

# Build the application
RUN npm run build:cli

# Production stage
FROM node:18-alpine AS production

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# Create app user
RUN addgroup -g 1001 -S architekAI && \
    adduser -S architekAI -u 1001

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/templates ./templates
COPY --from=builder /app/config ./config

# Copy additional files
COPY README.md LICENSE ./

# Create directories for user data
RUN mkdir -p /app/diagrams /app/logs /home/<USER>/.architek-ai && \
    chown -R architekAI:architekAI /app /home/<USER>

# Switch to non-root user
USER architekAI

# Set environment variables
ENV NODE_ENV=production
ENV LOG_LEVEL=info
ENV ARCHITEKTI_OUTPUT_DIR=/app/diagrams

# Expose port for web interface
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node dist/cli/index.js --version || exit 1

# Default command
CMD ["node", "dist/cli/index.js"]

# Web server variant
FROM production AS web

# Expose web port
EXPOSE 3000

# Start web server
CMD ["node", "dist/web/api/server.js"]

# CLI variant (default)
FROM production AS cli

# Set entrypoint for CLI usage
ENTRYPOINT ["node", "dist/cli/index.js"]

# Default to help command
CMD ["--help"]
