# Monolithic Architecture Template
# Traditional monolithic architecture with layered design and single database

id: "monolithic"
name: "Monolithic Architecture"
description: "Traditional monolithic architecture with layered design and single database"
pattern: "monolithic"
version: "1.0.0"
author: "ArchitekAI"

metadata:
  tags:
    - "monolithic"
    - "traditional"
    - "simple"
    - "single-deployment"
  complexity: "simple"
  useCases:
    - "small to medium applications"
    - "rapid prototyping"
    - "simple business logic"
    - "team learning projects"
  prerequisites:
    - "basic web development"
    - "database design"
  documentation: |
    This template provides a foundation for monolithic architecture with:
    - Single deployable unit
    - Layered architecture (presentation, business, data)
    - Centralized database
    - Simple deployment and testing

components:
  - id: "web-client"
    name: "Web Client"
    type: "web_app"
    description: "Frontend web application"
    properties:
      framework: "React/Angular/Vue"
      port: 3000
    position:
      x: 200
      y: 50

  - id: "load-balancer"
    name: "Load Balancer"
    type: "load_balancer"
    description: "Distributes traffic across application instances"
    properties:
      algorithm: "round-robin"
      healthCheck: true
    position:
      x: 200
      y: 150

  - id: "application-server"
    name: "Application Server"
    type: "service"
    description: "Main application containing all business logic"
    properties:
      port: 8080
      layers: ["presentation", "business", "data"]
      framework: "Spring Boot/Express/Django"
    position:
      x: 200
      y: 250

  - id: "database"
    name: "Database"
    type: "database"
    description: "Centralized database for all application data"
    properties:
      type: "postgresql"
      port: 5432
      schemas: ["users", "orders", "products", "audit"]
    position:
      x: 200
      y: 350

  - id: "cache"
    name: "Cache"
    type: "cache"
    description: "Application-level caching for performance"
    properties:
      type: "redis"
      port: 6379
      ttl: 3600
    position:
      x: 400
      y: 250

connections:
  - id: "client-to-lb"
    source: "web-client"
    target: "load-balancer"
    type: "https"
    label: "User Requests"

  - id: "lb-to-app"
    source: "load-balancer"
    target: "application-server"
    type: "http"
    label: "Application Requests"

  - id: "app-to-db"
    source: "application-server"
    target: "database"
    type: "database_connection"
    label: "Data Operations"

  - id: "app-to-cache"
    source: "application-server"
    target: "cache"
    type: "tcp"
    label: "Cache Operations"

customization:
  allowComponentAddition: true
  allowComponentRemoval: true
  allowConnectionModification: true
  requiredComponents:
    - "application-server"
    - "database"
  optionalComponents:
    - "cache"
    - "load-balancer"
  configurationSchema:
    databaseType:
      type: "string"
      enum: ["postgresql", "mysql", "sqlite"]
      default: "postgresql"
    caching:
      type: "boolean"
      default: true
    loadBalancing:
      type: "boolean"
      default: false
