/**
 * Unit tests for ConfigManager
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import path from 'path';
import { ConfigManager } from '@/core/config/manager';
import { testUtils } from '../setup';

describe('ConfigManager', () => {
  let configManager: ConfigManager;
  let tempDir: string;

  beforeEach(async () => {
    tempDir = await testUtils.createTempDir('config-test');
    configManager = new ConfigManager();
  });

  afterEach(async () => {
    await testUtils.cleanupTempDir(tempDir);
  });

  describe('initialization', () => {
    it('should initialize with default configuration', async () => {
      await configManager.initialize();
      const config = configManager.getConfig();
      
      expect(config).toBeDefined();
      expect(config.version).toBe('1.0.0');
      expect(config.output.defaultFormat).toBe('mermaid');
      expect(config.logging.level).toBe('info');
    });

    it('should load configuration from file', async () => {
      const customConfig = {
        output: {
          defaultFormat: 'plantuml',
          directory: './custom-diagrams',
        },
        logging: {
          level: 'debug',
        },
      };

      await testUtils.createTestConfig(tempDir, customConfig);
      
      // Change working directory temporarily
      const originalCwd = process.cwd();
      process.chdir(tempDir);
      
      try {
        await configManager.initialize();
        const config = configManager.getConfig();
        
        expect(config.output.defaultFormat).toBe('plantuml');
        expect(config.output.directory).toBe('./custom-diagrams');
        expect(config.logging.level).toBe('debug');
      } finally {
        process.chdir(originalCwd);
      }
    });
  });

  describe('configuration management', () => {
    beforeEach(async () => {
      await configManager.initialize();
    });

    it('should set configuration values', async () => {
      await configManager.set('output.defaultFormat', 'ascii');
      await configManager.set('logging.level', 'warn');
      
      const config = configManager.getConfig();
      expect(config.output.defaultFormat).toBe('ascii');
      expect(config.logging.level).toBe('warn');
    });

    it('should set nested configuration values', async () => {
      await configManager.set('web.port', 4000);
      await configManager.set('nlp.confidence.minimum', 0.8);
      
      const config = configManager.getConfig();
      expect(config.web.port).toBe(4000);
      expect(config.nlp.confidence.minimum).toBe(0.8);
    });

    it('should create nested objects when setting deep paths', async () => {
      await configManager.set('custom.nested.value', 'test');
      
      const config = configManager.getConfig();
      expect((config as any).custom.nested.value).toBe('test');
    });
  });

  describe('validation', () => {
    beforeEach(async () => {
      await configManager.initialize();
    });

    it('should validate valid configuration', async () => {
      const validation = await configManager.validate();
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect invalid configuration values', async () => {
      await configManager.set('logging.level', 'invalid-level');
      
      const validation = await configManager.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]?.path).toContain('logging.level');
    });

    it('should detect invalid output format', async () => {
      await configManager.set('output.defaultFormat', 'invalid-format');
      
      const validation = await configManager.validate();
      
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]?.path).toContain('output.defaultFormat');
    });
  });

  describe('file operations', () => {
    beforeEach(async () => {
      await configManager.initialize();
    });

    it('should save configuration to file', async () => {
      const originalCwd = process.cwd();
      process.chdir(tempDir);
      
      try {
        await configManager.set('output.defaultFormat', 'svg');
        await configManager.save();
        
        const fs = await import('fs-extra');
        const configPath = path.join(tempDir, '.architekAI.yaml');
        const exists = await fs.pathExists(configPath);
        
        expect(exists).toBe(true);
        
        const content = await fs.readFile(configPath, 'utf8');
        expect(content).toContain('defaultFormat: svg');
      } finally {
        process.chdir(originalCwd);
      }
    });

    it('should reset configuration', async () => {
      await configManager.set('output.defaultFormat', 'plantuml');
      await configManager.set('logging.level', 'debug');
      
      let config = configManager.getConfig();
      expect(config.output.defaultFormat).toBe('plantuml');
      expect(config.logging.level).toBe('debug');
      
      await configManager.reset();
      
      config = configManager.getConfig();
      expect(config.output.defaultFormat).toBe('mermaid'); // default
      expect(config.logging.level).toBe('info'); // default
    });
  });

  describe('environment variables', () => {
    it('should load configuration from environment variables', async () => {
      // Set environment variables
      process.env.ARCHITEKTI_LOG_LEVEL = 'debug';
      process.env.ARCHITEKTI_OUTPUT_FORMAT = 'plantuml';
      process.env.ARCHITEKTI_WEB_PORT = '4000';
      
      try {
        await configManager.initialize();
        const config = configManager.getConfig();
        
        expect(config.logging.level).toBe('debug');
        expect(config.output.defaultFormat).toBe('plantuml');
        expect(config.web.port).toBe(4000);
      } finally {
        // Clean up environment variables
        delete process.env.ARCHITEKTI_LOG_LEVEL;
        delete process.env.ARCHITEKTI_OUTPUT_FORMAT;
        delete process.env.ARCHITEKTI_WEB_PORT;
      }
    });
  });

  describe('path methods', () => {
    it('should return correct config paths', () => {
      const globalPath = configManager.getGlobalConfigPath();
      const localPath = configManager.getLocalConfigPath();
      const activePath = configManager.getActiveConfigPath();
      
      expect(globalPath).toContain('.architekAI');
      expect(globalPath).toContain('config.yaml');
      expect(localPath).toContain('.architekAI.yaml');
      expect(activePath).toBe(localPath);
    });
  });

  describe('error handling', () => {
    it('should handle invalid YAML files gracefully', async () => {
      const fs = await import('fs-extra');
      const invalidConfigPath = path.join(tempDir, '.architekAI.yaml');
      
      // Write invalid YAML
      await fs.writeFile(invalidConfigPath, 'invalid: yaml: content: [', 'utf8');
      
      const originalCwd = process.cwd();
      process.chdir(tempDir);
      
      try {
        // Should not throw, but should log warning and use defaults
        await expect(configManager.initialize()).resolves.not.toThrow();
        
        const config = configManager.getConfig();
        expect(config.output.defaultFormat).toBe('mermaid'); // should use default
      } finally {
        process.chdir(originalCwd);
      }
    });

    it('should handle missing config files gracefully', async () => {
      // Should not throw when config files don't exist
      await expect(configManager.initialize()).resolves.not.toThrow();
      
      const config = configManager.getConfig();
      expect(config).toBeDefined();
      expect(config.version).toBe('1.0.0');
    });
  });
});
